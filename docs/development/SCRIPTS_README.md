# Scripts Directory

This directory contains all automation scripts for the FAAFO Career Platform,
organized by purpose and functionality.

## Directory Structure

```
scripts/
├── testing/           # Testing and quality assurance scripts (60+ files)
├── database/          # Database management and seeding scripts (15+ files)
├── security/          # Security auditing and protection scripts (8+ files)
├── deployment/        # Deployment and production scripts (7+ files)
├── analysis/          # Code analysis and improvement scripts (15+ files)
├── ai-api/           # AI service and API testing scripts (20+ files)
├── development/       # Development utilities and debugging (10+ files)
├── performance/       # Performance testing and monitoring (8+ files)
├── email/            # Email testing and verification (5+ files)
├── utilities/        # General utilities and helpers (12+ files)
└── README.md         # This file
```

## Script Categories

### 🧪 Testing Scripts (`testing/`)

**Purpose**: Comprehensive testing automation and quality assurance

**Key Scripts**:

- `comprehensive-test.ts` - Full application testing suite
- `test-api-comprehensive.ts` - Complete API endpoint testing
- `test-auth-e2e.ts` - End-to-end authentication testing
- `test-assessment-integration.ts` - Assessment system integration tests
- `master-testing-suite.js` - Master test orchestration
- `ai-integration-testing.js` - AI service integration testing

**Usage**: Run comprehensive tests across all application features

### 🗄️ Database Scripts (`database/`)

**Purpose**: Database management, seeding, and maintenance

**Key Scripts**:

- `seed-*.ts` - Data seeding scripts for various entities
- `setup-test-database.ts` - Test database initialization
- `database-schema-validation.ts` - Schema integrity validation
- `reset-users.ts` - User data reset utilities
- `check-foreign-keys.ts` - Foreign key constraint validation

**Usage**: Database setup, seeding, and maintenance operations

### 🔒 Security Scripts (`security/`)

**Purpose**: Security auditing, protection, and vulnerability testing

**Key Scripts**:

- `security-audit.js` - Comprehensive security audit
- `penetration-test.js` - Automated penetration testing
- `add-csrf-protection.js` - CSRF protection implementation
- `audit-csrf-protection.js` - CSRF protection validation

**Usage**: Security testing and protection implementation

### 🚀 Deployment Scripts (`deployment/`)

**Purpose**: Production deployment and environment management

**Key Scripts**:

- `deploy-to-vercel.sh` - Vercel deployment automation
- `production-readiness-check.js` - Pre-deployment validation
- `deployment-checklist.ts` - Deployment verification checklist
- `backup_database.sh` - Database backup automation

**Usage**: Production deployment and environment validation

### 📊 Analysis Scripts (`analysis/`)

**Purpose**: Code analysis, optimization, and quality improvement

**Key Scripts**:

- `analyze-bundle-size.js` - Bundle size analysis and optimization
- `analyze-circular-dependencies.js` - Circular dependency detection
- `analyze-typescript-errors.ts` - TypeScript error analysis
- `fix-error-handling.ts` - Error handling improvements
- `validate-environment.ts` - Environment validation

**Usage**: Code quality analysis and automated improvements

### 🤖 AI/API Scripts (`ai-api/`)

**Purpose**: AI service testing and API contract validation

**Key Scripts**:

- `api-contract-verification.ts` - API contract validation
- `migrate-api-error-handling.ts` - API error handling migration

**Usage**: AI service integration and API validation

### 🛠️ Development Scripts (`development/`)

**Purpose**: Development utilities and debugging tools

**Key Scripts**:

- `auth-debug-comprehensive.ts` - Authentication debugging
- `restore-authentication.ts` - Authentication restoration
- `demo-phase3-features.ts` - Feature demonstration

**Usage**: Development debugging and feature demonstration

### ⚡ Performance Scripts (`performance/`)

**Purpose**: Performance testing and monitoring

**Key Scripts**:

- `load-test-skill-gap.js` - Skill gap analysis load testing
- `performance-monitor.js` - Performance monitoring utilities
- `validate-performance.js` - Performance validation

**Usage**: Performance testing and optimization

### 📧 Email Scripts (`email/`)

**Purpose**: Email testing and verification

**Key Scripts**:

- `verify-user-email.js` - User email verification testing
- `final-verification.ts` - Final verification processes

**Usage**: Email system testing and verification

### 🔧 Utilities (`utilities/`)

**Purpose**: General utilities and helper scripts

**Key Scripts**:

- `consolidate-documentation.sh` - Documentation consolidation
- `find-all-docs.sh` - Documentation discovery
- `clear-rate-limits.js` - Rate limit management
- `curate-quality-resources.js` - Resource quality curation

**Usage**: General maintenance and utility operations

## Usage Guidelines

### Running Scripts

**From Project Root**:

```bash
# Testing scripts
node scripts/testing/comprehensive-test.ts
tsx scripts/testing/test-api-comprehensive.ts

# Database scripts
tsx scripts/database/seed-forum-categories.ts
node scripts/database/setup-test-database.ts

# Security scripts
node scripts/security/security-audit.js
node scripts/security/penetration-test.js

# Deployment scripts
./scripts/deployment/deploy-to-vercel.sh
node scripts/deployment/production-readiness-check.js
```

### Script Dependencies

**Common Requirements**:

- Node.js 18+ for JavaScript files
- TypeScript/tsx for .ts files
- Bash shell for .sh files
- Database connection for database scripts
- Environment variables for API scripts

### Environment Setup

**Required Environment Variables**:

```bash
DATABASE_URL=          # Database connection
NEXTAUTH_SECRET=       # Authentication secret
GEMINI_API_KEY=       # AI service key
SMTP_*=               # Email configuration
```

## Maintenance

### Adding New Scripts

1. Place in appropriate category directory
2. Follow naming conventions (verb-noun-purpose)
3. Add documentation header with purpose and usage
4. Update this README with new script information

### Script Naming Conventions

- **Testing**: `test-feature-type.ts`
- **Database**: `seed-entity.ts`, `setup-purpose.ts`
- **Security**: `audit-feature.js`, `add-protection.js`
- **Analysis**: `analyze-aspect.js`, `fix-issue.ts`
- **Utilities**: `verb-noun.js`

### Regular Maintenance

- Review and clean up obsolete scripts monthly
- Update documentation when adding new scripts
- Test critical scripts after major changes
- Archive old scripts that are no longer needed

## Integration

### CI/CD Integration

Many scripts are designed for CI/CD pipeline integration:

- Testing scripts provide exit codes for pipeline decisions
- Security scripts generate reports for compliance
- Deployment scripts handle production releases

### Development Workflow

Scripts support various development workflows:

- Local testing and debugging
- Database setup and seeding
- Performance monitoring
- Security validation

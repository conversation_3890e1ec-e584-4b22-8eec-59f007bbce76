# Debug Directory

This directory contains debugging artifacts and tools for the FAAFO Career
Platform.

## Directory Structure

```
debug/
├── scripts/           # Debugging scripts and utilities
├── screenshots/       # Error and debugging screenshots
│   └── testerat_enhanced/  # Screenshots from testerat testing framework
├── logs/             # Debug logs (future use)
└── README.md         # This file
```

## Scripts Directory

### Assessment Debugging

- `debug-assessment-service.js` - Tests the AlgorithmicAssessmentService with
  mock data
- `debug-assessment-database.js` - Debugs assessment database records and status

### Authentication Debugging

- `debug-auth-system.ts` - Comprehensive authentication system debugging
- `debug-login-flow.ts` - Login flow specific debugging
- `debug-signup-validation.ts` - Signup validation debugging

### API Debugging

- `debug-api-call.ts` - General API call debugging utility

### User/Database Debugging

- `debug-user-issue.js` - Debugs user, session, and account database issues

## Screenshots Directory

### Main Screenshots (July 13, 2025)

- `debug-error.png` - General error state screenshot
- `debug-final-state.png` - Final application state screenshot
- `debug-login-page.png` - Login page debugging screenshot
- `debug-password-validation.png` - Password validation error screenshot
- `debug-signup-validation.png` - Signup validation error screenshot

### Testerat Enhanced Screenshots (July 9, 2025)

- `comprehensive_test_error.png` - Comprehensive test error
- `error_screenshot.png` - Generic error screenshot
- `error_screenshot_*.png` - Timestamped error screenshots from automated
  testing

## Usage Guidelines

1. **Before Running Scripts**: Ensure you have the proper environment variables
   set
2. **Database Scripts**: Make sure your DATABASE_URL is configured correctly
3. **Screenshots**: These are historical debugging artifacts - do not delete
   without review
4. **New Debug Files**: Add new debugging artifacts to the appropriate
   subdirectory

## Maintenance

- Review and clean up old debug files monthly
- Keep recent debugging artifacts (last 30 days) for reference
- Document any new debugging procedures in this README

## Related Directories

- `/scripts/` - Main application scripts
- `/logs/` - Application runtime logs
- `/test-results/` - Test execution results
- `/screenshots/` - Application screenshots (non-debug)

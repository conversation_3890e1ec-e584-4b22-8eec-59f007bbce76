# TypeScript Standards for FAAFO Career Platform

This document establishes comprehensive TypeScript coding standards and best
practices specific to the FAAFO Career Platform.

## 🎯 **Core TypeScript Principles**

### **1. Strict Type Safety**

```typescript
// tsconfig.json - Strict mode enabled
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### **2. No `any` Types**

```typescript
// ❌ Avoid
function processData(data: any): any {
  return data.someProperty;
}

// ✅ Preferred
function processData<T extends Record<string, unknown>>(data: T): T[keyof T] {
  return data[Object.keys(data)[0]];
}

// ✅ Alternative with proper typing
interface ProcessableData {
  id: string;
  value: unknown;
}

function processData(data: ProcessableData): unknown {
  return data.value;
}
```

## 🏗️ **Interface Design Standards**

### **Interface Naming Conventions**

```typescript
// ✅ Descriptive interface names
interface UserProfile {
  id: string;
  email: string;
  name: string;
}

// ✅ Service interfaces with 'I' prefix
interface IUserService {
  createUser(data: CreateUserData): Promise<User>;
  getUserById(id: string): Promise<User | null>;
}

// ✅ Response interfaces with clear naming
interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: ApiError;
}

interface UserListResponse extends ApiResponse<User[]> {
  pagination: PaginationInfo;
}
```

### **Interface Composition Patterns**

```typescript
// ✅ Base interfaces for common properties
interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TimestampedEntity {
  createdAt: Date;
  updatedAt: Date;
}

// ✅ Extend base interfaces
interface User extends BaseEntity {
  email: string;
  name: string;
  profile?: UserProfile;
}

// ✅ Composition over inheritance when appropriate
interface UserWithProfile {
  user: User;
  profile: UserProfile;
  permissions: Permission[];
}
```

### **Optional vs Required Properties**

```typescript
// ✅ Clear distinction between required and optional
interface CreateUserRequest {
  // Required fields
  email: string;
  password: string;

  // Optional fields
  name?: string;
  profilePicture?: string;
}

interface UpdateUserRequest {
  // All fields optional for updates
  email?: string;
  name?: string;
  profilePicture?: string;
}

// ✅ Use Partial<T> for update operations
type UpdateUserData = Partial<Pick<User, 'name' | 'email' | 'profilePicture'>>;
```

## 🔧 **Utility Types & Generics**

### **Common Utility Type Patterns**

```typescript
// ✅ API response wrapper
type ApiResult<T> =
  | {
      success: true;
      data: T;
    }
  | {
      success: false;
      error: ApiError;
    };

// ✅ Database entity creation
type CreateEntity<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;

// ✅ Pagination wrapper
interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ✅ Service method options
interface ServiceOptions {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTTL?: number;
}
```

### **Generic Constraints**

```typescript
// ✅ Constrain generics appropriately
interface Repository<T extends BaseEntity> {
  create(data: CreateEntity<T>): Promise<T>;
  findById(id: string): Promise<T | null>;
  update(id: string, data: Partial<T>): Promise<T>;
  delete(id: string): Promise<void>;
}

// ✅ Multiple constraints
interface Validator<T extends Record<string, unknown>, R = ValidationResult> {
  validate(data: T): R;
  sanitize(data: T): T;
}

// ✅ Conditional types for complex scenarios
type ApiEndpoint<T> = T extends 'GET'
  ? { method: 'GET'; response: unknown }
  : T extends 'POST'
    ? { method: 'POST'; body: unknown; response: unknown }
    : never;
```

## 🎨 **Component Type Patterns**

### **React Component Props**

```typescript
// ✅ Component props with proper typing
interface ButtonProps extends React.ComponentProps<'button'> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

// ✅ Polymorphic component pattern
interface PolymorphicProps<T extends React.ElementType> {
  as?: T;
  children: React.ReactNode;
}

type ComponentProps<T extends React.ElementType> = PolymorphicProps<T> &
  Omit<React.ComponentProps<T>, keyof PolymorphicProps<T>>;

// ✅ Forward ref typing
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'md', ...props }, ref) => {
    return <button ref={ref} {...props} />;
  }
);
```

### **Hook Type Patterns**

```typescript
// ✅ Custom hook return types
interface UseApiResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

function useApi<T>(url: string): UseApiResult<T> {
  // Implementation
}

// ✅ Hook options pattern
interface UseApiOptions {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  retry?: number;
  retryDelay?: number;
}

function useApiWithOptions<T>(
  url: string,
  options: UseApiOptions = {}
): UseApiResult<T> {
  // Implementation
}
```

## 🔒 **Type Guards & Validation**

### **Type Guard Patterns**

```typescript
// ✅ Type guards for runtime validation
function isUser(value: unknown): value is User {
  return (
    typeof value === 'object' &&
    value !== null &&
    'id' in value &&
    'email' in value &&
    typeof (value as User).id === 'string' &&
    typeof (value as User).email === 'string'
  );
}

// ✅ Generic type guard
function hasProperty<T, K extends string>(
  obj: T,
  prop: K
): obj is T & Record<K, unknown> {
  return typeof obj === 'object' && obj !== null && prop in obj;
}

// ✅ Array type guard
function isArrayOf<T>(
  value: unknown,
  guard: (item: unknown) => item is T
): value is T[] {
  return Array.isArray(value) && value.every(guard);
}
```

### **Zod Integration**

```typescript
// ✅ Zod schema with TypeScript inference
import { z } from 'zod';

const UserSchema = z.object({
  id: z.string().cuid(),
  email: z.string().email().max(254),
  name: z.string().min(1).max(100),
  createdAt: z.date(),
  profile: z
    .object({
      bio: z.string().optional(),
      avatar: z.string().url().optional()
    })
    .optional()
});

// ✅ Infer TypeScript type from Zod schema
type User = z.infer<typeof UserSchema>;

// ✅ Validation function with proper typing
function validateUser(data: unknown): User {
  return UserSchema.parse(data);
}

function validateUserSafe(
  data: unknown
): { success: true; data: User } | { success: false; error: z.ZodError } {
  const result = UserSchema.safeParse(data);
  return result.success
    ? { success: true, data: result.data }
    : { success: false, error: result.error };
}
```

## 🚀 **Async/Promise Patterns**

### **Promise Type Patterns**

```typescript
// ✅ Service method signatures
interface UserService {
  createUser(data: CreateUserData): Promise<User>;
  getUserById(id: string): Promise<User | null>;
  updateUser(id: string, data: UpdateUserData): Promise<User>;
  deleteUser(id: string): Promise<void>;
  listUsers(options: ListOptions): Promise<PaginatedResponse<User>>;
}

// ✅ Error handling with Result type
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

async function safeApiCall<T>(apiCall: () => Promise<T>): Promise<Result<T>> {
  try {
    const data = await apiCall();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}
```

### **Async Iterator Patterns**

```typescript
// ✅ Async generator typing
async function* fetchUsersPaginated(
  pageSize: number = 10
): AsyncGenerator<User[], void, unknown> {
  let page = 1;
  let hasMore = true;

  while (hasMore) {
    const response = await fetchUsers({ page, limit: pageSize });
    yield response.items;
    hasMore = response.pagination.hasNext;
    page++;
  }
}

// ✅ Usage with proper typing
async function processAllUsers(): Promise<void> {
  for await (const userBatch of fetchUsersPaginated()) {
    // userBatch is properly typed as User[]
    await processBatch(userBatch);
  }
}
```

## 📊 **Enum & Union Type Standards**

### **Enum Patterns**

```typescript
// ✅ String enums for better debugging
enum UserRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  USER = 'user',
  GUEST = 'guest'
}

// ✅ Const assertions for literal types
const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  PENDING: 'pending'
} as const;

type UserStatus = (typeof USER_STATUS)[keyof typeof USER_STATUS];
```

### **Discriminated Union Patterns**

```typescript
// ✅ Discriminated unions for state management
type LoadingState = {
  status: 'loading';
  progress?: number;
};

type SuccessState = {
  status: 'success';
  data: unknown;
  timestamp: Date;
};

type ErrorState = {
  status: 'error';
  error: Error;
  retryCount: number;
};

type AsyncState = LoadingState | SuccessState | ErrorState;

// ✅ Type-safe state handling
function handleAsyncState(state: AsyncState): string {
  switch (state.status) {
    case 'loading':
      return `Loading... ${state.progress ?? 0}%`;
    case 'success':
      return `Success: ${JSON.stringify(state.data)}`;
    case 'error':
      return `Error: ${state.error.message} (Retry: ${state.retryCount})`;
    default:
      // TypeScript ensures exhaustive checking
      const _exhaustive: never = state;
      return _exhaustive;
  }
}
```

## 🔍 **Advanced Type Patterns**

### **Mapped Types**

```typescript
// ✅ Create readonly versions
type ReadonlyUser = Readonly<User>;

// ✅ Make all properties optional
type PartialUser = Partial<User>;

// ✅ Pick specific properties
type UserSummary = Pick<User, 'id' | 'name' | 'email'>;

// ✅ Omit sensitive properties
type PublicUser = Omit<User, 'password' | 'passwordResetToken'>;

// ✅ Custom mapped type
type Nullable<T> = {
  [K in keyof T]: T[K] | null;
};

type OptionalExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
```

### **Template Literal Types**

```typescript
// ✅ API endpoint typing
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';
type ApiVersion = 'v1' | 'v2';
type ApiEndpoint = `/api/${ApiVersion}/${string}`;

// ✅ Event naming pattern
type EventType = 'user' | 'order' | 'payment';
type EventAction = 'created' | 'updated' | 'deleted';
type EventName = `${EventType}:${EventAction}`;

// ✅ CSS class naming
type Size = 'sm' | 'md' | 'lg';
type Variant = 'primary' | 'secondary';
type ButtonClass = `btn-${Variant}-${Size}`;
```

## 📝 **Documentation Standards**

### **TSDoc Comments**

````typescript
/**
 * Analyzes user skills and provides personalized career recommendations
 *
 * @param userId - The unique identifier for the user
 * @param skills - Array of the user's current skills
 * @param preferences - User's career preferences and constraints
 * @returns Promise that resolves to an array of career recommendations
 *
 * @throws {ValidationError} When input parameters are invalid
 * @throws {ServiceUnavailableError} When the AI service is unavailable
 *
 * @example
 * ```typescript
 * const recommendations = await analyzeCareerPath(
 *   'user-123',
 *   ['JavaScript', 'React', 'Node.js'],
 *   {
 *     preferredIndustry: 'technology',
 *     workLocation: 'remote',
 *     salaryRange: { min: 80000, max: 120000 }
 *   }
 * );
 * ```
 *
 * @since 1.0.0
 * @version 1.2.0
 */
async function analyzeCareerPath(
  userId: string,
  skills: string[],
  preferences: CareerPreferences
): Promise<CareerRecommendation[]> {
  // Implementation
}
````

### **Type Documentation**

```typescript
/**
 * Represents a user's career assessment results
 *
 * @interface AssessmentResult
 */
interface AssessmentResult {
  /** Unique identifier for the assessment */
  id: string;

  /** The user who completed the assessment */
  userId: string;

  /**
   * Overall score from 0-100 indicating career readiness
   * @minimum 0
   * @maximum 100
   */
  overallScore: number;

  /**
   * Detailed breakdown of skills and competencies
   * @see SkillAssessment
   */
  skillBreakdown: SkillAssessment[];

  /**
   * Recommended next steps for career development
   * @example ["Complete JavaScript course", "Build portfolio project"]
   */
  recommendations: string[];
}
```

This TypeScript standards document ensures type safety, consistency, and
maintainability across the FAAFO Career Platform codebase.

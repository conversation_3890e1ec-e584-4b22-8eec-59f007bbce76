# API Design Standards for FAAFO Career Platform

This document establishes comprehensive API design standards and best practices
for the FAAFO Career Platform.

## 🎯 **Core API Principles**

### **1. RESTful Design**

- **Resource-Based URLs**: Use nouns, not verbs in URLs
- **HTTP Methods**: Use appropriate HTTP methods for actions
- **Stateless**: Each request contains all necessary information
- **Cacheable**: Responses should be cacheable when appropriate

### **2. Consistency**

- **Uniform Response Format**: All APIs return consistent response structure
- **Error Handling**: Standardized error responses across all endpoints
- **Naming Conventions**: Consistent naming for parameters and fields
- **Versioning**: Clear API versioning strategy

### **3. Security First**

- **Authentication**: All endpoints require proper authentication
- **Authorization**: Role-based access control
- **Input Validation**: Comprehensive input validation and sanitization
- **Rate Limiting**: Protect against abuse and ensure fair usage

## 🛣️ **URL Design Standards**

### **Resource Naming**

```
✅ Good Examples:
GET /api/v1/users
GET /api/v1/users/123
POST /api/v1/users
PUT /api/v1/users/123
DELETE /api/v1/users/123

GET /api/v1/users/123/assessments
POST /api/v1/users/123/assessments
GET /api/v1/assessments/456

❌ Bad Examples:
GET /api/v1/getUsers
POST /api/v1/createUser
GET /api/v1/user_list
DELETE /api/v1/removeUser/123
```

### **URL Structure Patterns**

```
/api/{version}/{resource}
/api/{version}/{resource}/{id}
/api/{version}/{resource}/{id}/{sub-resource}
/api/{version}/{resource}/{id}/{sub-resource}/{sub-id}

Examples:
/api/v1/users/123/assessments/456
/api/v1/learning-paths/789/progress
/api/v1/career-paths/101/recommendations
```

### **Query Parameters**

```typescript
// ✅ Standardized query parameters
interface StandardQueryParams {
  // Pagination
  page?: number; // Page number (1-based)
  limit?: number; // Items per page (max 100)
  offset?: number; // Alternative to page

  // Sorting
  sort?: string; // Field to sort by
  order?: 'asc' | 'desc'; // Sort direction

  // Filtering
  filter?: string; // JSON string for complex filters
  search?: string; // Text search query

  // Field selection
  fields?: string; // Comma-separated field list
  include?: string; // Related resources to include
  exclude?: string; // Fields to exclude
}

// Example usage:
// GET /api/v1/users?page=2&limit=20&sort=createdAt&order=desc
// GET /api/v1/users?search=john&filter={"role":"admin"}
// GET /api/v1/users?fields=id,name,email&include=profile
```

## 📊 **Response Format Standards**

### **Success Response Structure**

```typescript
interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  meta?: {
    requestId: string;
    timestamp: string;
    version: string;
    cached?: boolean;
    cacheExpiry?: string;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Examples:
// Single resource
{
  "success": true,
  "data": {
    "id": "user-123",
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "meta": {
    "requestId": "req-456",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}

// Collection with pagination
{
  "success": true,
  "data": [
    { "id": "user-123", "name": "John Doe" },
    { "id": "user-124", "name": "Jane Smith" }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  },
  "meta": {
    "requestId": "req-789",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}
```

### **Error Response Structure**

```typescript
interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    field?: string;        // For validation errors
    documentation?: string; // Link to docs
  };
  meta: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

// Examples:
// Validation Error
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "field": "email",
    "details": {
      "provided": "invalid-email",
      "expected": "Valid email address"
    }
  },
  "meta": {
    "requestId": "req-error-123",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}

// Authentication Error
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_ERROR",
    "message": "Invalid or expired token",
    "documentation": "https://docs.faafo.com/auth"
  },
  "meta": {
    "requestId": "req-error-456",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}
```

## 🔒 **Authentication & Authorization**

### **Authentication Methods**

```typescript
// JWT Bearer Token (Primary)
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// API Key (For service-to-service)
X-API-Key: your-api-key-here

// Session-based (For web app)
Cookie: session-id=abc123...
```

### **Authorization Patterns**

```typescript
// Role-based access control
interface UserPermissions {
  role: 'admin' | 'moderator' | 'user' | 'guest';
  permissions: string[];
  resourceAccess?: {
    [resourceType: string]: {
      read: boolean;
      write: boolean;
      delete: boolean;
    };
  };
}

// Resource-level authorization
// GET /api/v1/users/123 - Can access if:
// - User is admin
// - User is accessing their own profile
// - User has 'users:read' permission

// Endpoint-level authorization
// POST /api/v1/admin/users - Requires:
// - Admin role
// - 'admin:users:create' permission
```

## 🚦 **HTTP Status Codes**

### **Standard Status Code Usage**

```typescript
// Success Codes
200 OK          // Successful GET, PUT, PATCH
201 Created     // Successful POST
202 Accepted    // Async operation started
204 No Content  // Successful DELETE

// Client Error Codes
400 Bad Request          // Invalid request data
401 Unauthorized         // Authentication required
403 Forbidden           // Insufficient permissions
404 Not Found           // Resource doesn't exist
409 Conflict            // Resource conflict (duplicate)
422 Unprocessable Entity // Validation failed
429 Too Many Requests   // Rate limit exceeded

// Server Error Codes
500 Internal Server Error // Unexpected server error
502 Bad Gateway          // Upstream service error
503 Service Unavailable  // Service temporarily down
504 Gateway Timeout      // Upstream service timeout
```

### **Error Code Mapping**

```typescript
const HTTP_STATUS_MAP = {
  VALIDATION_ERROR: 422,
  AUTHENTICATION_ERROR: 401,
  AUTHORIZATION_ERROR: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  RATE_LIMIT_EXCEEDED: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;
```

## 🔄 **Rate Limiting Standards**

### **Rate Limit Tiers**

```typescript
interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Max requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// Standard rate limits
const RATE_LIMITS = {
  // Authentication endpoints
  AUTH: { windowMs: 15 * 60 * 1000, maxRequests: 5 },

  // General API endpoints
  GENERAL: { windowMs: 15 * 60 * 1000, maxRequests: 100 },

  // Resource creation
  CREATE: { windowMs: 15 * 60 * 1000, maxRequests: 10 },

  // High-frequency endpoints
  HIGH_FREQUENCY: { windowMs: 15 * 60 * 1000, maxRequests: 200 },

  // Admin endpoints
  ADMIN: { windowMs: 15 * 60 * 1000, maxRequests: 50 }
};
```

### **Rate Limit Headers**

```typescript
// Response headers for rate limiting
X-RateLimit-Limit: 100        // Requests allowed per window
X-RateLimit-Remaining: 95      // Requests remaining in window
X-RateLimit-Reset: 1642694400  // Unix timestamp when window resets
X-RateLimit-Window: 900        // Window size in seconds

// When rate limit exceeded
HTTP/1.1 429 Too Many Requests
Retry-After: 300               // Seconds to wait before retry
```

## 📝 **Request/Response Examples**

### **User Management API**

```typescript
// GET /api/v1/users
// Response: List of users with pagination
{
  "success": true,
  "data": [
    {
      "id": "user-123",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "createdAt": "2024-01-15T10:30:00Z",
      "profile": {
        "bio": "Software developer",
        "avatar": "https://example.com/avatar.jpg"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}

// POST /api/v1/users
// Request body:
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "securePassword123"
}

// Response:
{
  "success": true,
  "data": {
    "id": "user-124",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "user",
    "createdAt": "2024-01-15T10:35:00Z"
  }
}
```

### **Assessment API**

```typescript
// POST /api/v1/assessments
// Request body:
{
  "type": "career_assessment",
  "responses": {
    "question_1": "Software Development",
    "question_2": "5 years",
    "question_3": ["JavaScript", "Python", "React"]
  },
  "metadata": {
    "timeSpent": 1200,
    "completedAt": "2024-01-15T10:40:00Z"
  }
}

// Response:
{
  "success": true,
  "data": {
    "id": "assessment-456",
    "userId": "user-123",
    "type": "career_assessment",
    "status": "completed",
    "score": 85,
    "results": {
      "careerMatch": "Full Stack Developer",
      "confidence": 0.92,
      "recommendations": [
        "Strengthen backend development skills",
        "Learn cloud technologies"
      ]
    },
    "createdAt": "2024-01-15T10:40:00Z"
  }
}
```

## 🔍 **Validation Standards**

### **Input Validation**

```typescript
// Use Zod for request validation
import { z } from 'zod';

const CreateUserSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email().max(254),
  password: z.string().min(8).max(128),
  profile: z
    .object({
      bio: z.string().max(500).optional(),
      avatar: z.string().url().optional()
    })
    .optional()
});

// Validation middleware
export const validateRequest = (schema: z.ZodSchema) => {
  return async (request: NextRequest) => {
    const body = await request.json();
    const result = schema.safeParse(body);

    if (!result.success) {
      throw new ValidationError(result.error);
    }

    return result.data;
  };
};
```

### **Output Sanitization**

```typescript
// Sanitize sensitive data in responses
function sanitizeUser(user: User): PublicUser {
  const { password, passwordResetToken, ...publicUser } = user;
  return publicUser;
}

// Apply sanitization in API responses
export const GET = async (request: NextRequest) => {
  const users = await userService.getAllUsers();
  const sanitizedUsers = users.map(sanitizeUser);

  return NextResponse.json({
    success: true,
    data: sanitizedUsers
  });
};
```

## 📚 **Documentation Standards**

### **OpenAPI/Swagger Documentation**

```yaml
# Example OpenAPI specification
paths:
  /api/v1/users:
    get:
      summary: List users
      description: Retrieve a paginated list of users
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
```

### **API Documentation Requirements**

- **Endpoint Description**: Clear purpose and functionality
- **Request/Response Examples**: Realistic examples for all scenarios
- **Error Scenarios**: Document all possible error responses
- **Authentication**: Required permissions and authentication methods
- **Rate Limits**: Specify rate limiting rules
- **Deprecation Notices**: Clear migration paths for deprecated endpoints

This API design standards document ensures consistency, security, and usability
across all FAAFO Career Platform APIs.

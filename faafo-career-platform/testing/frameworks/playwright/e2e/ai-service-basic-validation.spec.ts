/**
 * Basic AI Service Validation Tests
 * Quick validation of core AI service functionality
 */

import { test, expect } from '@playwright/test';

test.describe('AI Service Basic Validation', () => {
  test('should load homepage successfully', async ({ page }) => {
    await page.goto('/');
    await expect(page.locator('h1').first()).toBeVisible();
    console.log('✅ Homepage loaded successfully');
  });

  test('should access login page', async ({ page }) => {
    await page.goto('/login');

    // Check if login form elements exist
    const emailInput = page.locator('input[name="email"], input[type="email"]');
    const passwordInput = page.locator('input[name="password"], input[type="password"]');
    const submitButton = page.locator(
      'button[type="submit"], button:has-text("Sign In"), button:has-text("Login")'
    );

    await expect(emailInput).toBeVisible();
    await expect(passwordInput).toBeVisible();
    await expect(submitButton).toBeVisible();

    console.log('✅ Login page elements found');
  });

  test('should access AI service health endpoint', async ({ page }) => {
    // Test the health check API directly
    const response = await page.request.get('/api/health');

    if (response.status() === 200) {
      const healthData = await response.json();
      console.log('✅ Health check successful:', healthData);

      // Basic health check validation
      expect(healthData).toBeDefined();
    } else {
      console.log('⚠️ Health endpoint returned status:', response.status());
    }
  });

  test('should test AI service with mock data', async ({ page }) => {
    // Navigate to a page that might use AI service
    await page.goto('/');

    // Look for any AI-related functionality on the homepage
    const aiElements = await page.locator('[data-testid*="ai"], [class*="ai"], [id*="ai"]').count();
    console.log(`Found ${aiElements} potential AI-related elements`);

    // Check if there are any career-related links
    const careerLinks = page.locator('a[href*="career"], a[href*="resume"], a[href*="interview"]');
    const careerLinkCount = await careerLinks.count();

    if (careerLinkCount > 0) {
      console.log(`✅ Found ${careerLinkCount} career-related navigation links`);

      // Try to click the first career-related link
      const firstLink = careerLinks.first();
      const href = await firstLink.getAttribute('href');
      console.log(`Attempting to navigate to: ${href}`);

      try {
        await firstLink.click();
        await page.waitForLoadState('networkidle', { timeout: 10000 });
        console.log('✅ Successfully navigated to career page');
      } catch (error) {
        console.log('⚠️ Navigation failed, but link exists:', error);
      }
    } else {
      console.log('ℹ️ No career-related links found on homepage');
    }
  });

  test('should validate AI service configuration', async ({ page }) => {
    // Test if the AI service endpoints are accessible
    const endpoints = [
      '/api/ai/resume-analysis',
      '/api/ai/career-recommendations',
      '/api/ai/interview-questions',
      '/api/health'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await page.request.get(endpoint);
        console.log(`${endpoint}: ${response.status()}`);

        // We expect some endpoints to require authentication (401/403)
        // or return method not allowed (405) for GET requests
        // The important thing is they respond (not 404)
        if (response.status() !== 404) {
          console.log(`✅ ${endpoint} is accessible (status: ${response.status()})`);
        } else {
          console.log(`❌ ${endpoint} not found (404)`);
        }
      } catch (error) {
        console.log(`⚠️ ${endpoint} error:`, error);
      }
    }
  });

  test('should test performance monitoring endpoints', async ({ page }) => {
    // Test admin endpoints (expect authentication required)
    const adminEndpoints = [
      '/api/admin/ai-performance-dashboard?view=overview',
      '/api/admin/ai-performance-dashboard?view=health'
    ];

    for (const endpoint of adminEndpoints) {
      try {
        const response = await page.request.get(endpoint);
        console.log(`${endpoint}: ${response.status()}`);

        // Admin endpoints should require authentication
        if (response.status() === 403 || response.status() === 401) {
          console.log(`✅ ${endpoint} properly protected (requires auth)`);
        } else if (response.status() === 200) {
          console.log(`✅ ${endpoint} accessible (might be in dev mode)`);
        } else {
          console.log(`⚠️ ${endpoint} unexpected status: ${response.status()}`);
        }
      } catch (error) {
        console.log(`⚠️ ${endpoint} error:`, error);
      }
    }
  });

  test('should validate caching and performance improvements', async ({ page }) => {
    // Test multiple requests to see if caching is working
    const testEndpoint = '/api/health';
    const requests = [];

    console.log('Testing request performance and caching...');

    for (let i = 0; i < 3; i++) {
      const startTime = Date.now();
      const response = await page.request.get(testEndpoint);
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      requests.push({
        attempt: i + 1,
        status: response.status(),
        responseTime: responseTime
      });

      console.log(`Request ${i + 1}: ${response.status()} in ${responseTime}ms`);
    }

    // Check if subsequent requests are faster (indicating caching)
    if (requests.length >= 2) {
      const firstRequest = requests[0].responseTime;
      const lastRequest = requests[requests.length - 1].responseTime;

      if (lastRequest < firstRequest * 0.8) {
        console.log('✅ Performance improvement detected (likely caching working)');
      } else {
        console.log('ℹ️ No significant performance improvement detected');
      }
    }
  });

  test('should test error handling and resilience', async ({ page }) => {
    // Test invalid endpoints to check error handling
    const invalidEndpoints = ['/api/ai/invalid-endpoint', '/api/nonexistent', '/invalid-page'];

    for (const endpoint of invalidEndpoints) {
      try {
        const response = await page.request.get(endpoint);
        console.log(`${endpoint}: ${response.status()}`);

        // Should return 404 for invalid endpoints
        if (response.status() === 404) {
          console.log(`✅ ${endpoint} properly returns 404`);
        } else {
          console.log(`⚠️ ${endpoint} unexpected status: ${response.status()}`);
        }
      } catch (error) {
        console.log(`⚠️ ${endpoint} error:`, error);
      }
    }
  });

  test('should validate security headers and protection', async ({ page }) => {
    const response = await page.request.get('/');
    const headers = response.headers();

    console.log('Checking security headers...');

    // Check for important security headers
    const securityHeaders = [
      'x-frame-options',
      'x-content-type-options',
      'x-xss-protection',
      'strict-transport-security'
    ];

    let securityScore = 0;
    for (const header of securityHeaders) {
      if (headers[header]) {
        console.log(`✅ ${header}: ${headers[header]}`);
        securityScore++;
      } else {
        console.log(`⚠️ Missing security header: ${header}`);
      }
    }

    console.log(`Security score: ${securityScore}/${securityHeaders.length}`);

    // At least some security headers should be present
    expect(securityScore).toBeGreaterThan(0);
  });

  test('should test rate limiting (if implemented)', async ({ page }) => {
    // Test rate limiting by making multiple rapid requests
    const endpoint = '/api/health';
    const rapidRequests = [];

    console.log('Testing rate limiting...');

    // Make 10 rapid requests
    for (let i = 0; i < 10; i++) {
      const promise = page.request.get(endpoint);
      rapidRequests.push(promise);
    }

    try {
      const responses = await Promise.all(rapidRequests);
      const statusCodes = responses.map(r => r.status());

      console.log('Rapid request status codes:', statusCodes);

      // Check if any requests were rate limited (429)
      const rateLimitedCount = statusCodes.filter(code => code === 429).length;

      if (rateLimitedCount > 0) {
        console.log(`✅ Rate limiting working: ${rateLimitedCount} requests rate limited`);
      } else {
        console.log('ℹ️ No rate limiting detected (might be disabled in dev mode)');
      }

      // Most requests should succeed
      const successCount = statusCodes.filter(code => code === 200).length;
      expect(successCount).toBeGreaterThan(0);
    } catch (error) {
      console.log('⚠️ Rate limiting test error:', error);
    }
  });
});

test.describe('AI Service Integration Validation', () => {
  test('should validate AI service improvements are working', async ({ page }) => {
    console.log('🔍 Validating AI Service Improvements...');

    // Test 1: Health Check
    const healthResponse = await page.request.get('/api/health');
    console.log(`Health Check: ${healthResponse.status()}`);

    // Test 2: Performance Monitoring
    try {
      const perfResponse = await page.request.get(
        '/api/admin/ai-performance-dashboard?view=overview'
      );
      console.log(`Performance Dashboard: ${perfResponse.status()}`);
    } catch (error) {
      console.log('Performance Dashboard: Requires authentication (expected)');
    }

    // Test 3: Basic Navigation
    await page.goto('/');
    const title = await page.title();
    console.log(`Page Title: ${title}`);
    expect(title).toBeTruthy();

    // Test 4: Check for AI-related functionality
    const pageContent = await page.content();
    const hasAIFeatures =
      pageContent.includes('AI') ||
      pageContent.includes('career') ||
      pageContent.includes('resume') ||
      pageContent.includes('interview');

    if (hasAIFeatures) {
      console.log('✅ AI-related content found on homepage');
    } else {
      console.log('ℹ️ No obvious AI-related content on homepage');
    }

    console.log('🎉 Basic AI Service validation completed!');
  });
});

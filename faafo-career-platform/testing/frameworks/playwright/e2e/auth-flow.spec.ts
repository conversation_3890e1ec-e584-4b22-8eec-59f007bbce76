import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test('should display signup form', async ({ page }) => {
    await page.goto('/');

    // Check if we're on mobile by viewport width
    const viewportSize = page.viewportSize();
    const isMobile = viewportSize && viewportSize.width < 1280; // xl breakpoint is 1280px

    if (isMobile) {
      // On mobile, open the mobile menu first
      await page.click('[aria-label*="Open main menu"]');
      await page.waitForSelector('#mobile-menu', { state: 'visible' });

      // Click Sign Up in mobile menu - use more specific selector
      await page.click('#mobile-menu a[href="/signup"]');
    } else {
      // On desktop, click the Sign Up button directly
      await page.click('text=Sign Up');
    }

    // Should navigate to signup page
    await expect(page).toHaveURL(/.*signup/);

    // Should show signup form
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]').first()).toBeVisible(); // Use .first() to avoid multiple matches
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should display login form', async ({ page }) => {
    await page.goto('/');

    // Check if we're on mobile by viewport width
    const viewportSize = page.viewportSize();
    const isMobile = viewportSize && viewportSize.width < 1280; // xl breakpoint is 1280px

    if (isMobile) {
      // On mobile, open the mobile menu first
      await page.click('[aria-label*="Open main menu"]');
      await page.waitForSelector('#mobile-menu', { state: 'visible' });

      // Click Log In in mobile menu - use button selector
      await page.click('#mobile-menu button:has-text("Log In")');
    } else {
      // On desktop, click the Log In button directly
      await page.click('text=Log In');
    }

    // Should navigate to login page or show login modal
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
  });

  test('should validate email format in signup', async ({ page }) => {
    await page.goto('/signup');

    // Fill invalid email - use a format that fails our custom validation
    await page.fill('input[type="email"]', 'test@'); // This will fail our validation
    await page.fill('input[id="password"]', 'ValidPassword123!'); // Use valid password to isolate email validation
    await page.fill('input[id="confirm-password"]', 'ValidPassword123!'); // Fill confirm password too

    // Try to submit
    await page.click('button[type="submit"]');

    // Wait for validation to process and show error
    await page.waitForTimeout(1000);

    // Should show email validation error specifically
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible();
  });

  test('should validate password requirements', async ({ page }) => {
    await page.goto('/signup');

    // Fill valid email but weak password
    await page.fill('input[type="email"]', '<EMAIL>'); // Use clearly valid email
    await page.fill('input[id="password"]', '123'); // Use specific ID
    await page.fill('input[id="confirm-password"]', '123'); // Fill confirm password too

    // Try to submit
    await page.click('button[type="submit"]');

    // Wait for validation to process and show error
    await page.waitForTimeout(1000);

    // Should show password validation error specifically
    await expect(page.locator('text=Password must be at least 8 characters long')).toBeVisible();
  });

  test('should handle signup flow', async ({ page }) => {
    await page.goto('/signup');

    // Fill valid signup data
    const testEmail = `test-${Date.now()}@example.com`;
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[id="password"]', 'SecurePassword123!'); // Use specific ID
    await page.fill('input[id="confirm-password"]', 'SecurePassword123!'); // Fill confirm password

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for form submission to complete
    await page.waitForLoadState('networkidle');

    // Wait a bit more for the success state to render
    await page.waitForTimeout(2000);

    // Check for success indicators - look for specific success elements
    const hasSuccessTitle = await page
      .locator('text=Registration Successful')
      .isVisible()
      .catch(() => false);
    const hasVerificationMessage = await page
      .locator('text=verification')
      .isVisible()
      .catch(() => false);
    const hasEmailMessage = await page
      .locator('text=email')
      .isVisible()
      .catch(() => false);
    const hasSuccessIcon = await page
      .locator('svg')
      .first()
      .isVisible()
      .catch(() => false); // The green checkmark

    // Debug: log what's actually on the page
    const pageContent = await page.textContent('body');
    console.log('Page content after signup:', pageContent?.substring(0, 500));

    expect(
      hasSuccessTitle || hasVerificationMessage || hasEmailMessage || hasSuccessIcon
    ).toBeTruthy();
  });

  test('should navigate between login and signup', async ({ page }) => {
    await page.goto('/signup');

    // Should have link to login - look for the specific link text
    await expect(page.locator('a[href="/login"]')).toBeVisible(); // Use href selector to be specific

    // Click login link
    await page.click('a[href="/login"]');

    // Should navigate to login
    await expect(page).toHaveURL(/.*login/);

    // Should have link back to signup - look for the specific link in the login page content (not navigation)
    await expect(page.locator('main a[href="/signup"], .card a[href="/signup"]')).toBeVisible();
  });
});

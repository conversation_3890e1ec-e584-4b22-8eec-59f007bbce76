import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load and display main navigation', async ({ page }) => {
    await page.goto('/');

    // Check if the page loads
    await expect(page).toHaveTitle(/FAAFO/);

    // Check main navigation elements
    await expect(page.locator('text=FAAFO')).toBeVisible();
    await expect(page.locator('text=Career Paths')).toBeVisible();
    await expect(page.locator('text=Resources')).toBeVisible();
    await expect(page.locator('text=Tools')).toBeVisible();

    // Check for login/signup buttons
    await expect(page.locator('text=Log In')).toBeVisible();
    await expect(page.locator('text=Sign Up')).toBeVisible();
  });

  test('should navigate to career paths page', async ({ page }) => {
    await page.goto('/');

    // Click on Career Paths
    await page.click('text=Career Paths');

    // Should navigate to career paths page
    await expect(page).toHaveURL(/.*career-paths/);

    // Should show career paths content
    await expect(page.locator('h1')).toContainText(/Career/);
  });

  test('should navigate to resources page', async ({ page }) => {
    await page.goto('/');

    // Click on Resources
    await page.click('text=Resources');

    // Should navigate to resources page
    await expect(page).toHaveURL(/.*resources/);

    // Should show resources content
    await expect(page.locator('h1')).toContainText(/Resources/);
  });

  test('should open tools dropdown', async ({ page }) => {
    await page.goto('/');

    // Click on Tools dropdown
    await page.click('text=Tools');

    // Should show dropdown items
    await expect(page.locator('text=Career Assessment')).toBeVisible();
    await expect(page.locator('text=Progress & Analytics')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');

    // Check if mobile menu button is visible
    await expect(page.locator('[aria-label="Toggle mobile menu"]')).toBeVisible();

    // Click mobile menu
    await page.click('[aria-label="Toggle mobile menu"]');

    // Check if mobile menu items are visible
    await expect(page.locator('text=Home')).toBeVisible();
    await expect(page.locator('text=Career Paths')).toBeVisible();
  });
});

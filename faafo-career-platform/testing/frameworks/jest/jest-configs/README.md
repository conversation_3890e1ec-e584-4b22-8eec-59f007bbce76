# Jest Configurations

This directory contains all Jest testing configurations for the FAAFO Career
Platform.

## Directory Structure

```
jest-configs/
├── jest.base.config.js           # Base configuration (shared settings)
├── jest.coverage.config.js       # Coverage thresholds and quality gates
├── jest.config.ci.js             # CI/CD pipeline configuration
├── jest.config.unit.js           # Unit testing configuration
├── jest.config.performance.js    # Performance testing configuration
├── jest.config.architecture.js   # Architecture validation tests
├── jest.simple.config.js         # Simple/basic testing configuration
├── jest.minimal.config.js        # Minimal configuration (replaces debug)
├── jest.setup.minimal.ts         # Minimal setup file
└── README.md                     # This file
```

## Configuration Files

### Core Configurations

**jest.base.config.js**

- Shared base configuration that other configs extend
- Common module mappings, transforms, and basic settings
- Not used directly, only as a base for other configs

**jest.coverage.config.js** (249 lines)

- Comprehensive coverage configuration with quality gates
- Environment-based coverage profiles (development, CI, production)
- Per-directory coverage thresholds for critical areas
- Coverage validation and quality gate functions

### Environment-Specific Configurations

**jest.config.ci.js**

- Optimized for CI/CD pipeline execution
- Strict coverage enforcement and quality gates
- Enhanced reporting for CI integration
- Memory-optimized settings for CI environments

**jest.config.unit.js**

- Fast unit test execution
- Excludes integration, performance, and e2e tests
- Optimized for developer workflow
- Moderate coverage thresholds

**jest.config.performance.js**

- Long-running performance and load tests
- Single worker to avoid resource contention
- Extended timeouts and memory limits
- Performance-specific reporting

**jest.config.architecture.js**

- Architecture validation and analysis tests
- File system access for code analysis
- Single worker for file system operations
- Node environment for better file access

### Simplified Configurations

**jest.simple.config.js**

- Basic configuration for simple testing scenarios
- Minimal setup with essential features only
- Good for quick testing and debugging

**jest.minimal.config.js**

- Lightweight Next.js-based configuration
- Replaces both old minimal and debug configs
- Environment variable controls for debug features
- Optimized for component testing

## Usage in Package.json

The configurations are used in package.json scripts:

```json
{
  "test": "jest --config jest-configs/jest.config.unit.js",
  "test:ci": "jest --config jest-configs/jest.config.ci.js",
  "test:performance": "jest --config jest-configs/jest.config.performance.js",
  "test:architecture": "jest --config jest-configs/jest.config.architecture.js"
}
```

## Coverage Profiles

The coverage configuration supports multiple profiles:

- **Development**: 70-75% coverage thresholds
- **CI**: 85-90% coverage thresholds
- **Production**: 95% coverage thresholds

Set via environment variables:

```bash
COVERAGE_PROFILE=production npm run test:ci
```

## Setup Files

**jest.setup.minimal.ts**

- Minimal setup for basic component testing
- Essential mocks for Next.js, NextAuth, and browser APIs
- Reduced noise from console warnings
- Memory-optimized for fast test execution

## Quality Gates

The coverage configuration includes quality gates:

- Fail on coverage decrease
- Critical files must maintain 100% coverage
- Minimum coverage increase requirements for new code

## Maintenance

### Adding New Configurations

1. Extend from `jest.base.config.js` when possible
2. Document the specific use case and optimizations
3. Update this README with the new configuration
4. Add corresponding package.json script

### Updating Coverage Thresholds

1. Modify `jest.coverage.config.js`
2. Test with different environments (dev, CI, production)
3. Ensure quality gates still function correctly

### Debugging Configuration Issues

1. Use `jest.minimal.config.js` for basic testing
2. Set `JEST_DEBUG=true` for enhanced debugging
3. Check file paths are correct after moves
4. Verify all required setup files exist

## Migration Notes

This directory consolidates configurations that were previously scattered:

- Removed duplicate `jest.config.minimal.js` and `jest.config.debug.js`
- Centralized all Jest configurations in one location
- Updated all path references to work from new location
- Maintained backward compatibility with existing scripts

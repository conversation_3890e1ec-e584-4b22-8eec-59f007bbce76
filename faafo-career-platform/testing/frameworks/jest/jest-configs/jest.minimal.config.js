/**
 * Minimal Jest Configuration
 * Lightweight configuration for basic testing and debugging
 * Replaces both jest.config.minimal.js and jest.config.debug.js
 */

const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './'
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/testing/frameworks/jest/jest-configs/jest.setup.minimal.ts'],
  testEnvironment: 'jsdom',
  testMatch: ['**/__tests__/**/*.test.tsx'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}'
  ],
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }]
  },
  transformIgnorePatterns: ['/node_modules/', '^.+\\.module\\.(css|sass|scss)$'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  testTimeout: 10000,

  // Debug-specific settings (can be enabled via environment variable)
  verbose: process.env.JEST_VERBOSE === 'true',
  detectOpenHandles: process.env.JEST_DEBUG === 'true',
  logHeapUsage: process.env.JEST_DEBUG === 'true'
};

module.exports = createJestConfig(customJestConfig);

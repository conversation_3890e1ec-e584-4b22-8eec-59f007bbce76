/**
 * Jest Configuration for Performance Tests
 * Optimized for long-running performance and load tests
 */

const baseConfig = require('../../../../jest.config.js');

module.exports = {
  ...baseConfig,

  // Set root directory to project root
  rootDir: '../../../../',

  // Override paths to be relative to project root
  setupFiles: ['<rootDir>/jest.polyfills.js'],

  // Performance test specific settings
  testTimeout: 120000, // 2 minutes for performance tests
  maxWorkers: 1, // Single worker to avoid resource contention
  workerIdleMemoryLimit: '1GB',

  // Only run performance tests
  testMatch: [
    '**/__tests__/performance/**/*.(test|spec).(ts|tsx|js|jsx)',
    '**/performance/**/*.(test|spec).(ts|tsx|js|jsx)'
  ],

  // Remove path ignores for performance tests
  testPathIgnorePatterns: ['/node_modules/', '/coverage/'],

  // Disable coverage for performance tests
  collectCoverage: false,

  // Enhanced logging for performance monitoring
  verbose: true,
  logHeapUsage: true,

  // Custom reporters for performance metrics
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: './testing/test-results/performance',
        outputName: 'performance-junit.xml'
      }
    ],
    ['<rootDir>/testing/test-utils/performance-reporter.js']
  ],

  // Setup files specific to performance testing
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.ts',
    '<rootDir>/testing/test-utils/performance-setup.js'
  ],

  // Environment variables for performance tests
  testEnvironment: 'node', // Use node environment for better performance

  // Global test configuration
  globals: {
    'ts-jest': {
      tsconfig: {
        jsx: 'react-jsx'
      }
    },
    PERFORMANCE_TEST_MODE: true,
    TEST_TIMEOUT_MULTIPLIER: 2
  }
};

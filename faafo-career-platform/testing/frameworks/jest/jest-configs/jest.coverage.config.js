/**
 * Jest Coverage Configuration
 * Production-ready coverage thresholds and quality gates
 *
 * This configuration implements comprehensive coverage requirements
 * with different thresholds for different environments and code areas.
 */

// Environment-based coverage profiles
const COVERAGE_PROFILES = {
  development: {
    global: {
      branches: 70,
      functions: 75,
      lines: 75,
      statements: 75
    }
  },

  ci: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },

  production: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  }
};

// Get current environment
const environment = process.env.NODE_ENV || 'development';
const isCi = process.env.CI === 'true';
const coverageProfile = process.env.COVERAGE_PROFILE;
const isProduction = environment === 'production' || coverageProfile === 'production';

// Select appropriate profile
let selectedProfile;
if (coverageProfile && COVERAGE_PROFILES[coverageProfile]) {
  selectedProfile = coverageProfile;
} else if (isProduction) {
  selectedProfile = 'production';
} else if (isCi) {
  selectedProfile = 'ci';
} else {
  selectedProfile = 'development';
}

const baseCoverage = COVERAGE_PROFILES[selectedProfile];

/**
 * Production-ready coverage configuration with per-directory thresholds
 */
const coverageConfig = {
  // Global thresholds - minimum requirements for entire codebase
  global: baseCoverage.global,

  // Per-directory thresholds - higher standards for critical areas
  './src/lib/auth.ts': {
    branches: 100,
    functions: 100,
    lines: 100,
    statements: 100
  },

  './src/lib/security/': {
    branches: 100,
    functions: 100,
    lines: 100,
    statements: 100
  },

  './src/lib/services/': {
    branches: 95,
    functions: 95,
    lines: 95,
    statements: 95
  },

  './src/app/api/': {
    branches: 90,
    functions: 95,
    lines: 90,
    statements: 90
  },

  './src/components/': {
    branches: 85,
    functions: 90,
    lines: 85,
    statements: 85
  },

  './src/lib/': {
    branches: 90,
    functions: 95,
    lines: 90,
    statements: 90
  },

  './src/hooks/': {
    branches: 85,
    functions: 90,
    lines: 85,
    statements: 85
  }
};

/**
 * Files to collect coverage from
 */
const collectCoverageFrom = [
  // Core application files
  'src/**/*.{ts,tsx}',
  'components/**/*.{ts,tsx}',
  'lib/**/*.{ts,tsx}',
  'middleware.ts',

  // Exclude patterns
  '!src/**/*.d.ts',
  '!src/**/*.stories.{ts,tsx}',
  '!src/**/*.config.{ts,js}',
  '!**/__tests__/**',
  '!**/__mocks__/**',
  '!**/node_modules/**',
  '!**/coverage/**',
  '!**/dist/**',
  '!**/build/**',
  '!**/.next/**',

  // Exclude configuration files
  '!jest.config*.js',
  '!jest.setup.js',
  '!jest.polyfills.js',
  '!next.config.js',
  '!tailwind.config.js',
  '!postcss.config.js',

  // Exclude test utilities
  '!**/testing/**',
  '!**/fixtures/**',

  // Exclude generated files
  '!**/prisma/generated/**',
  '!**/.prisma/**',
  '!**/migrations/**',

  // Exclude development tools
  '!**/scripts/**',
  '!**/tools/**',
  '!**/docs/**',

  // Exclude specific patterns
  '!src/app/**/layout.tsx', // Layout files often have minimal logic
  '!src/app/**/loading.tsx', // Loading components are usually simple
  '!src/app/**/error.tsx', // Error boundaries have specific patterns
  '!src/app/**/not-found.tsx', // 404 pages are usually static

  // Exclude barrel exports (index files that just re-export)
  '!**/index.{ts,tsx}',

  // Exclude type-only files
  '!src/types/**',
  '!**/*.types.{ts,tsx}',
  '!**/*.interface.{ts,tsx}'
];

/**
 * Coverage reporters configuration
 */
const getCoverageReporters = () => {
  const baseReporters = ['text-summary', 'lcov'];

  if (isCi) {
    baseReporters.push('json', 'clover');
  }

  if (isProduction || process.env.COVERAGE_REPORT === 'full') {
    baseReporters.push('html', 'text');
  }

  return baseReporters;
};

/**
 * Quality gates configuration
 */
const qualityGates = {
  // Fail fast on critical coverage drops
  failOnCoverageDecrease: true,

  // Minimum coverage increase for new code
  minimumCoverageIncrease: isProduction ? 2 : 0,

  // Maximum allowed coverage decrease
  maximumCoverageDecrease: 1,

  // Critical files that must maintain 100% coverage
  criticalFiles: ['src/lib/auth.ts', 'src/lib/security/**', 'src/lib/db.ts']
};

module.exports = {
  coverageConfig,
  collectCoverageFrom,
  coverageReporters: getCoverageReporters(),
  qualityGates,
  selectedProfile,

  // Export for use in Jest configs
  getCoverageThreshold: () => coverageConfig,
  getCollectCoverageFrom: () => collectCoverageFrom,
  getCoverageReporters: getCoverageReporters,

  // Utility functions
  isProductionCoverage: () => isProduction,
  isCiCoverage: () => isCi,
  getCurrentProfile: () => selectedProfile,

  // Validation function
  validateCoverage: coverageResults => {
    const errors = [];
    const warnings = [];

    // Check global thresholds
    const globalThresholds = coverageConfig.global;
    Object.entries(globalThresholds).forEach(([metric, threshold]) => {
      const actual = coverageResults.global?.[metric];
      if (actual < threshold) {
        errors.push(`Global ${metric} coverage ${actual}% below threshold ${threshold}%`);
      }
    });

    return { errors, warnings, passed: errors.length === 0 };
  }
};

/**
 * Jest Configuration for CI/CD Pipeline
 * Enforces production-ready coverage thresholds and quality gates
 */

// Set CI environment before loading configs
process.env.CI = 'true';
process.env.NODE_ENV = 'test';

const baseConfig = require('../../../../jest.config.js');
const coverageConfig = require('./jest.coverage.config.js');

module.exports = {
  ...baseConfig,

  // Set root directory to project root
  rootDir: '../../../../',

  // Override paths to be relative to project root
  setupFiles: ['<rootDir>/jest.polyfills.js'],

  // CI-specific optimizations
  maxWorkers: 2, // Limited workers for CI environment
  workerIdleMemoryLimit: '256MB',
  cache: false, // Disable cache in CI for clean builds

  // Strict timeout for CI
  testTimeout: 30000,

  // Enable coverage collection
  collectCoverage: true,

  // Use production-ready coverage configuration
  collectCoverageFrom: coverageConfig.getCollectCoverageFrom(),
  coverageReporters: [
    'text-summary',
    'lcov',
    'json',
    'clover',
    'cobertura' // For CI integration
  ],

  // Enforce CI coverage thresholds
  coverageThreshold: coverageConfig.getCoverageThreshold(),

  // Fail fast on coverage issues
  bail: 1, // Stop on first failure in CI

  // Enhanced reporting for CI
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: './test-results/ci',
        outputName: 'ci-junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true
      }
    ]
  ],

  // Memory management for CI
  logHeapUsage: true,
  detectOpenHandles: true,
  forceExit: true,

  // Verbose output for CI debugging
  verbose: true,

  // CI environment globals
  globals: {
    ...baseConfig.globals,
    CI: true,
    COVERAGE_ENFORCEMENT: true,
    QUALITY_GATES_ENABLED: true
  },

  // Setup files for CI
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.ts',
    '<rootDir>/testing/test-utils/memory-leak-fixes.js',
    '<rootDir>/testing/test-utils/ci-setup.js'
  ],

  // Custom test environment for CI
  testEnvironment: 'jsdom',
  testEnvironmentOptions: {
    url: 'http://localhost:3000'
  },

  // Coverage directory for CI artifacts
  coverageDirectory: './coverage/ci',

  // Additional CI-specific patterns
  testPathIgnorePatterns: [
    ...baseConfig.testPathIgnorePatterns,
    '/__tests__/manual/',
    '/__tests__/experimental/'
  ],

  // Transform configuration optimized for CI
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: {
          jsx: 'react-jsx',
          skipLibCheck: true // Faster CI builds
        }
      }
    ]
  }
};

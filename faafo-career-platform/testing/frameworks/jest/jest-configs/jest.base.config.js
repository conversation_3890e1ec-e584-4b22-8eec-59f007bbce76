/**
 * Base Jest Configuration
 * Shared configuration that other Jest configs extend
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',

  // Module resolution
  moduleNameMapper: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    '^@/emails/(.*)$': '<rootDir>/src/emails/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/services/(.*)$': '<rootDir>/src/lib/services/$1',
    '^@/utils/(.*)$': '<rootDir>/src/lib/utils/$1',
    '^@/lib/services/cacheService$': '<rootDir>/src/lib/cache',
    // CSS and static files
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js'
  },

  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: {
          jsx: 'react-jsx'
        }
      }
    ],
    '^.+\\.(js|jsx)$': ['babel-jest']
  },

  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(jose|openid-client|next-auth|@panva|oauth4webapi|@radix-ui|@auth)/)'
  ],

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Handle ES modules
  extensionsToTreatAsEsm: ['.ts', '.tsx'],

  // Basic test discovery
  testMatch: ['**/__tests__/**/*.test.(ts|tsx|js|jsx)', '**/*.test.(ts|tsx|js|jsx)'],

  // Common ignores
  testPathIgnorePatterns: ['/node_modules/', '/coverage/'],

  // Setup files
  setupFiles: ['<rootDir>/jest.polyfills.js'],

  // Basic performance settings
  testTimeout: 30000,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Memory management
  detectOpenHandles: true,
  forceExit: true
};

#!/bin/bash

# Forum Reaction Race Condition Test Script
# This script tests the race condition fix by making concurrent API calls

echo "🧪 Testing Forum Reaction Race Condition Fix..."
echo "=============================================="

# Configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/forum/reactions"

# First, let's get a session cookie by logging in
echo "📋 Step 1: Getting session information..."

# Get CSRF token first
CSRF_RESPONSE=$(curl -s -c cookies.txt "${BASE_URL}/api/csrf-token")
echo "CSRF Response: $CSRF_RESPONSE"

# Login to get session
LOGIN_RESPONSE=$(curl -s -b cookies.txt -c cookies.txt \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123!"}' \
  "${BASE_URL}/api/auth/callback/credentials")

echo "Login Response: $LOGIN_RESPONSE"

# Get session info
SESSION_RESPONSE=$(curl -s -b cookies.txt "${BASE_URL}/api/auth/session")
echo "Session Response: $SESSION_RESPONSE"

# Get a test post ID
echo ""
echo "📋 Step 2: Getting test post ID..."
POSTS_RESPONSE=$(curl -s -b cookies.txt "${BASE_URL}/api/forum/posts")
echo "Posts Response: $POSTS_RESPONSE"

# Extract first post ID (this is a simplified extraction - in real scenario you'd parse JSON properly)
POST_ID=$(echo "$POSTS_RESPONSE" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [ -z "$POST_ID" ]; then
    echo "❌ No posts found. Please create a test post first."
    exit 1
fi

echo "✅ Using test post ID: $POST_ID"

# Test 1: Concurrent identical reactions
echo ""
echo "🔄 Test 1: Making 5 concurrent identical LIKE reactions..."

# Make 5 concurrent requests
for i in {1..5}; do
    (
        RESPONSE=$(curl -s -b cookies.txt \
          -X POST \
          -H "Content-Type: application/json" \
          -d "{\"postId\":\"$POST_ID\",\"type\":\"LIKE\"}" \
          "${BASE_URL}${API_ENDPOINT}")
        echo "Request $i: $RESPONSE"
    ) &
done

# Wait for all background jobs to complete
wait

echo ""
echo "✅ Test 1 completed. Check the responses above."
echo "   Expected: All requests should succeed, but only 1 reaction should exist in database."

# Test 2: Concurrent different reactions
echo ""
echo "🔄 Test 2: Making concurrent different reactions (LIKE, LOVE, DISLIKE)..."

# Make concurrent requests with different reaction types
(
    RESPONSE=$(curl -s -b cookies.txt \
      -X POST \
      -H "Content-Type: application/json" \
      -d "{\"postId\":\"$POST_ID\",\"type\":\"LIKE\"}" \
      "${BASE_URL}${API_ENDPOINT}")
    echo "LIKE Request: $RESPONSE"
) &

(
    RESPONSE=$(curl -s -b cookies.txt \
      -X POST \
      -H "Content-Type: application/json" \
      -d "{\"postId\":\"$POST_ID\",\"type\":\"LOVE\"}" \
      "${BASE_URL}${API_ENDPOINT}")
    echo "LOVE Request: $RESPONSE"
) &

(
    RESPONSE=$(curl -s -b cookies.txt \
      -X POST \
      -H "Content-Type: application/json" \
      -d "{\"postId\":\"$POST_ID\",\"type\":\"DISLIKE\"}" \
      "${BASE_URL}${API_ENDPOINT}")
    echo "DISLIKE Request: $RESPONSE"
) &

# Wait for all background jobs to complete
wait

echo ""
echo "✅ Test 2 completed. Check the responses above."
echo "   Expected: All requests should succeed, but only 1 reaction should exist (last one wins)."

# Test 3: Toggle test
echo ""
echo "🔄 Test 3: Testing toggle behavior with concurrent requests..."

# First, ensure we have a LIKE reaction
INITIAL_RESPONSE=$(curl -s -b cookies.txt \
  -X POST \
  -H "Content-Type: application/json" \
  -d "{\"postId\":\"$POST_ID\",\"type\":\"LIKE\"}" \
  "${BASE_URL}${API_ENDPOINT}")
echo "Initial LIKE: $INITIAL_RESPONSE"

# Now make 3 concurrent LIKE requests (should toggle)
for i in {1..3}; do
    (
        RESPONSE=$(curl -s -b cookies.txt \
          -X POST \
          -H "Content-Type: application/json" \
          -d "{\"postId\":\"$POST_ID\",\"type\":\"LIKE\"}" \
          "${BASE_URL}${API_ENDPOINT}")
        echo "Toggle Request $i: $RESPONSE"
    ) &
done

# Wait for all background jobs to complete
wait

echo ""
echo "✅ Test 3 completed. Check the responses above."
echo "   Expected: Consistent toggle behavior - either 0 or 1 reaction, never duplicates."

# Cleanup
echo ""
echo "🧹 Cleaning up..."
rm -f cookies.txt

echo ""
echo "🎉 Race condition testing completed!"
echo ""
echo "📊 Summary:"
echo "   - The race condition fix uses atomic transactions with Prisma"
echo "   - Concurrent requests should not create duplicate reactions"
echo "   - The database constraints ensure data integrity"
echo "   - Check the API responses above to verify no duplicates were created"
echo ""
echo "🔍 To verify the fix worked:"
echo "   1. Check that all API responses show success (200 status)"
echo "   2. Verify in the database that only 1 reaction exists per user/post combination"
echo "   3. No P2002 (unique constraint violation) errors should occur"

/**
 * Architecture Test Setup
 * Global setup for architecture validation and analysis
 */

const fs = require('fs');
const path = require('path');

// Global architecture analysis utilities
global.architectureUtils = {
  projectRoot: process.cwd(),
  sourceDirectories: ['src', 'components', 'lib', 'app'],
  excludePatterns: [
    'node_modules',
    '.next',
    'coverage',
    '.git',
    'test-results',
    '__tests__',
    '*.test.*',
    '*.spec.*'
  ]
};

// File system analysis utilities
global.analyzeFileStructure = (directory = global.architectureUtils.projectRoot) => {
  const structure = {
    files: [],
    directories: [],
    totalFiles: 0,
    totalDirectories: 0,
    fileTypes: {},
    largeFiles: [],
    emptyFiles: []
  };

  const analyzeDirectory = (dir, relativePath = '') => {
    try {
      const items = fs.readdirSync(dir);

      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const relativeItemPath = path.join(relativePath, item);

        // Skip excluded patterns
        if (
          global.architectureUtils.excludePatterns.some(
            pattern => relativeItemPath.includes(pattern) || item.includes(pattern)
          )
        ) {
          return;
        }

        const stats = fs.statSync(fullPath);

        if (stats.isDirectory()) {
          structure.directories.push(relativeItemPath);
          structure.totalDirectories++;
          analyzeDirectory(fullPath, relativeItemPath);
        } else {
          const fileInfo = {
            path: relativeItemPath,
            size: stats.size,
            extension: path.extname(item),
            modified: stats.mtime
          };

          structure.files.push(fileInfo);
          structure.totalFiles++;

          // Track file types
          const ext = fileInfo.extension || 'no-extension';
          structure.fileTypes[ext] = (structure.fileTypes[ext] || 0) + 1;

          // Track large files (>1MB)
          if (stats.size > 1024 * 1024) {
            structure.largeFiles.push(fileInfo);
          }

          // Track empty files
          if (stats.size === 0) {
            structure.emptyFiles.push(fileInfo);
          }
        }
      });
    } catch (error) {
      console.warn(
        `Warning: Could not analyze directory ${dir}:`,
        error instanceof Error ? error.message : String(error)
      );
    }
  };

  analyzeDirectory(directory);
  return structure;
};

// Code quality analysis
global.analyzeCodeQuality = filePath => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    const analysis = {
      filePath,
      totalLines: lines.length,
      codeLines: 0,
      commentLines: 0,
      blankLines: 0,
      complexity: 0,
      imports: [],
      exports: [],
      functions: [],
      classes: [],
      issues: []
    };

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      if (trimmedLine === '') {
        analysis.blankLines++;
      } else if (
        trimmedLine.startsWith('//') ||
        trimmedLine.startsWith('/*') ||
        trimmedLine.startsWith('*')
      ) {
        analysis.commentLines++;
      } else {
        analysis.codeLines++;

        // Analyze imports
        if (trimmedLine.startsWith('import ')) {
          analysis.imports.push(trimmedLine);
        }

        // Analyze exports
        if (trimmedLine.startsWith('export ')) {
          analysis.exports.push(trimmedLine);
        }

        // Analyze functions
        if (
          trimmedLine.includes('function ') ||
          (trimmedLine.includes('const ') && trimmedLine.includes('=>'))
        ) {
          analysis.functions.push({
            line: index + 1,
            content: trimmedLine
          });
        }

        // Analyze classes
        if (trimmedLine.startsWith('class ')) {
          analysis.classes.push({
            line: index + 1,
            content: trimmedLine
          });
        }

        // Calculate complexity (simplified)
        if (
          trimmedLine.includes('if ') ||
          trimmedLine.includes('for ') ||
          trimmedLine.includes('while ') ||
          trimmedLine.includes('switch ')
        ) {
          analysis.complexity++;
        }

        // Detect potential issues
        if (trimmedLine.length > 120) {
          analysis.issues.push({
            type: 'long-line',
            line: index + 1,
            message: `Line exceeds 120 characters (${trimmedLine.length})`
          });
        }

        if (trimmedLine.includes('console.log')) {
          analysis.issues.push({
            type: 'debug-code',
            line: index + 1,
            message: 'Debug console.log found'
          });
        }

        if (trimmedLine.includes('TODO') || trimmedLine.includes('FIXME')) {
          analysis.issues.push({
            type: 'todo',
            line: index + 1,
            message: 'TODO/FIXME comment found'
          });
        }
      }
    });

    return analysis;
  } catch (error) {
    return {
      filePath,
      error: error instanceof Error ? error.message : String(error),
      issues: [
        {
          type: 'file-error',
          message: `Could not analyze file: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
};

// Dependency analysis
global.analyzeDependencies = () => {
  const analysis = {
    packageJson: null,
    dependencies: {},
    devDependencies: {},
    totalDependencies: 0,
    outdatedPackages: [],
    securityIssues: [],
    duplicates: []
  };

  try {
    const packageJsonPath = path.join(global.architectureUtils.projectRoot, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      analysis.packageJson = packageJson;
      analysis.dependencies = packageJson.dependencies || {};
      analysis.devDependencies = packageJson.devDependencies || {};
      analysis.totalDependencies =
        Object.keys(analysis.dependencies).length + Object.keys(analysis.devDependencies).length;
    }
  } catch (error) {
    analysis.error = error instanceof Error ? error.message : String(error);
  }

  return analysis;
};

// Performance metrics for architecture tests
global.measureArchitecturePerformance = async (operation, label) => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  try {
    const result = await operation();

    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

    console.log(`🏗️  ${label}: ${duration}ms, ${Math.round(memoryDelta / 1024 / 1024)}MB`);

    return {
      result,
      metrics: {
        duration,
        memoryDelta,
        memoryUsed: endMemory.heapUsed
      }
    };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.error(
      `❌ ${label} failed after ${duration}ms:`,
      error instanceof Error ? error.message : String(error)
    );
    throw error;
  }
};

// Architecture validation helpers
global.validateArchitecture = {
  hasValidStructure: expectedDirectories => {
    const structure = global.analyzeFileStructure();
    const missingDirectories = expectedDirectories.filter(
      dir => !structure.directories.some(existingDir => existingDir.includes(dir))
    );

    if (missingDirectories.length > 0) {
      throw new Error(`Missing expected directories: ${missingDirectories.join(', ')}`);
    }

    return true;
  },

  hasValidFileTypes: expectedTypes => {
    const structure = global.analyzeFileStructure();
    const missingTypes = expectedTypes.filter(
      type => !structure.fileTypes[type] || structure.fileTypes[type] === 0
    );

    if (missingTypes.length > 0) {
      throw new Error(`Missing expected file types: ${missingTypes.join(', ')}`);
    }

    return true;
  },

  hasNoLargeFiles: (maxSizeMB = 1) => {
    const structure = global.analyzeFileStructure();
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    const largeFiles = structure.largeFiles.filter(file => file.size > maxSizeBytes);

    if (largeFiles.length > 0) {
      const fileList = largeFiles.map(f => `${f.path} (${Math.round(f.size / 1024 / 1024)}MB)`);
      throw new Error(`Large files detected: ${fileList.join(', ')}`);
    }

    return true;
  }
};

// Setup and teardown for architecture tests
beforeEach(() => {
  console.log(`🏗️  Starting architecture test: ${expect.getState().currentTestName}`);
});

afterEach(() => {
  // Force cleanup
  if (global.gc) {
    global.gc();
  }
});

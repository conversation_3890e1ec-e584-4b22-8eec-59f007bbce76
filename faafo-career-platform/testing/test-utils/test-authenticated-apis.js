#!/usr/bin/env node

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001';

async function testAuthenticatedAPIs() {
  console.log('🔐 Testing Authenticated API Endpoints');
  console.log('='.repeat(50));

  // First, let's try to get a session token by logging in
  console.log('\n1. Getting CSRF Token...');

  try {
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf`);
    const csrfData = await csrfResponse.json();
    console.log('✅ CSRF Token:', csrfData.csrfToken ? 'Retrieved' : 'Failed');

    // Try to sign in
    console.log('\n2. Attempting Login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': csrfData.csrfToken
      },
      body: new URLSearchParams({
        email: '<EMAIL>',
        password: 'TestPassword123!',
        csrfToken: csrfData.csrfToken,
        callbackUrl: `${BASE_URL}/dashboard`,
        json: 'true'
      })
    });

    console.log('Login Response Status:', loginResponse.status);
    const loginData = await loginResponse.text();
    console.log('Login Response:', loginData.substring(0, 200));

    // Get session
    console.log('\n3. Getting Session...');
    const sessionResponse = await fetch(`${BASE_URL}/api/auth/session`, {
      headers: {
        Cookie: loginResponse.headers.get('set-cookie') || ''
      }
    });

    const sessionData = await sessionResponse.json();
    console.log('Session Status:', sessionResponse.status);
    console.log('Session Data:', JSON.stringify(sessionData, null, 2));

    if (!sessionData.user) {
      console.log('❌ No valid session found. Testing unauthenticated endpoints...');

      // Test assessment endpoint without auth
      console.log('\n4. Testing /api/assessment (unauthenticated)...');
      const assessmentResponse = await fetch(`${BASE_URL}/api/assessment?status=true`);
      console.log('Assessment Status:', assessmentResponse.status);
      const assessmentData = await assessmentResponse.json();
      console.log('Assessment Response:', JSON.stringify(assessmentData, null, 2));

      return;
    }

    // Test authenticated endpoints
    const cookies = loginResponse.headers.get('set-cookie') || '';

    console.log('\n4. Testing /api/assessment (authenticated)...');
    const assessmentResponse = await fetch(`${BASE_URL}/api/assessment?status=true`, {
      headers: {
        Cookie: cookies
      }
    });
    console.log('Assessment Status:', assessmentResponse.status);
    const assessmentData = await assessmentResponse.json();
    console.log('Assessment Response:', JSON.stringify(assessmentData, null, 2));

    console.log('\n5. Testing /api/freedom-fund (authenticated)...');
    const freedomFundResponse = await fetch(`${BASE_URL}/api/freedom-fund`, {
      headers: {
        Cookie: cookies
      }
    });
    console.log('Freedom Fund Status:', freedomFundResponse.status);
    const freedomFundData = await freedomFundResponse.json();
    console.log('Freedom Fund Response:', JSON.stringify(freedomFundData, null, 2));

    console.log('\n6. Testing /api/ai/skills-analysis/comprehensive (authenticated)...');
    const skillsAnalysisResponse = await fetch(`${BASE_URL}/api/ai/skills-analysis/comprehensive`, {
      method: 'POST',
      headers: {
        Cookie: cookies,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        targetCareerPath: {
          careerPathName: 'Full Stack Developer',
          targetLevel: 'INTERMEDIATE'
        },
        preferences: {
          timeframe: '6_MONTHS',
          hoursPerWeek: 10
        },
        includeMarketData: true
      })
    });
    console.log('Skills Analysis Status:', skillsAnalysisResponse.status);
    const skillsAnalysisData = await skillsAnalysisResponse.json();
    console.log('Skills Analysis Response:', JSON.stringify(skillsAnalysisData, null, 2));
  } catch (error) {
    console.error('❌ Error testing APIs:', error);
  }
}

async function testDirectDatabaseConnection() {
  console.log('\n🗄️  Testing Direct Database Connection');
  console.log('='.repeat(50));

  try {
    // Test the health endpoint which should test database
    const healthResponse = await fetch(`${BASE_URL}/api/health`);
    const healthData = await healthResponse.json();

    console.log('Health Status:', healthResponse.status);
    console.log('Database Status:', healthData.services?.database?.status);
    console.log('Database Response Time:', healthData.services?.database?.responseTime);

    if (healthData.services?.database?.status !== 'up') {
      console.log('❌ Database connection issue detected');
    } else {
      console.log('✅ Database connection healthy');
    }
  } catch (error) {
    console.error('❌ Error testing database:', error);
  }
}

async function main() {
  await testDirectDatabaseConnection();
  await testAuthenticatedAPIs();
}

main().catch(console.error);

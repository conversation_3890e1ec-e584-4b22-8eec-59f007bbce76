/**
 * Performance Test Setup
 * Global setup for performance monitoring and optimization
 */

// Global performance monitoring
global.performanceMetrics = {
  testStartTime: null,
  testStartMemory: null,
  memorySnapshots: [],
  timeouts: new Set(),
  intervals: new Set(),
  openHandles: new Set()
};

// Enhanced beforeEach for performance monitoring
beforeEach(() => {
  global.performanceMetrics.testStartTime = Date.now();
  global.performanceMetrics.testStartMemory = process.memoryUsage();

  // Clear any existing timers
  global.performanceMetrics.timeouts.clear();
  global.performanceMetrics.intervals.clear();
  global.performanceMetrics.openHandles.clear();

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
});

// Enhanced afterEach for cleanup and monitoring
afterEach(() => {
  const endTime = Date.now();
  const endMemory = process.memoryUsage();

  const duration = endTime - global.performanceMetrics.testStartTime;
  const memoryDelta = endMemory.heapUsed - global.performanceMetrics.testStartMemory.heapUsed;

  // Log performance warnings
  if (duration > 10000) {
    // 10 seconds
    console.warn(`⚠️  Slow test detected: ${expect.getState().currentTestName} (${duration}ms)`);
  }

  if (memoryDelta > 50 * 1024 * 1024) {
    // 50MB
    console.warn(
      `🧠 Memory-intensive test: ${expect.getState().currentTestName} (+${Math.round(memoryDelta / 1024 / 1024)}MB)`
    );
  }

  // Clean up any remaining timers
  global.performanceMetrics.timeouts.forEach(clearTimeout);
  global.performanceMetrics.intervals.forEach(clearInterval);

  // Force cleanup
  if (global.gc) {
    global.gc();
  }
});

// Override setTimeout to track timeouts
const originalSetTimeout = global.setTimeout;
global.setTimeout = (callback, delay, ...args) => {
  const timeoutId = originalSetTimeout(callback, delay, ...args);
  global.performanceMetrics.timeouts.add(timeoutId);
  return timeoutId;
};

// Override clearTimeout to track cleanup
const originalClearTimeout = global.clearTimeout;
global.clearTimeout = timeoutId => {
  global.performanceMetrics.timeouts.delete(timeoutId);
  return originalClearTimeout(timeoutId);
};

// Override setInterval to track intervals
const originalSetInterval = global.setInterval;
global.setInterval = (callback, delay, ...args) => {
  const intervalId = originalSetInterval(callback, delay, ...args);
  global.performanceMetrics.intervals.add(intervalId);
  return intervalId;
};

// Override clearInterval to track cleanup
const originalClearInterval = global.clearInterval;
global.clearInterval = intervalId => {
  global.performanceMetrics.intervals.delete(intervalId);
  return originalClearInterval(intervalId);
};

// Memory monitoring utilities
global.takeMemorySnapshot = (label = 'snapshot') => {
  const memory = process.memoryUsage();
  global.performanceMetrics.memorySnapshots.push({
    label,
    timestamp: Date.now(),
    memory,
    heapUsedMB: Math.round(memory.heapUsed / 1024 / 1024),
    heapTotalMB: Math.round(memory.heapTotal / 1024 / 1024),
    externalMB: Math.round(memory.external / 1024 / 1024)
  });
  return memory;
};

// Performance assertion helpers
global.expectPerformance = {
  toBeFasterThan: maxDuration => {
    const duration = Date.now() - global.performanceMetrics.testStartTime;
    if (duration > maxDuration) {
      throw new Error(`Test took ${duration}ms, expected less than ${maxDuration}ms`);
    }
  },

  toUseMemoryLessThan: maxMemoryMB => {
    const currentMemory = process.memoryUsage();
    const memoryDelta = currentMemory.heapUsed - global.performanceMetrics.testStartMemory.heapUsed;
    const memoryDeltaMB = Math.round(memoryDelta / 1024 / 1024);

    if (memoryDeltaMB > maxMemoryMB) {
      throw new Error(`Test used ${memoryDeltaMB}MB, expected less than ${maxMemoryMB}MB`);
    }
  },

  toHaveNoMemoryLeaks: () => {
    // Force garbage collection
    if (global.gc) {
      global.gc();
    }

    // Check for remaining timers
    if (global.performanceMetrics.timeouts.size > 0) {
      console.warn(`⚠️  ${global.performanceMetrics.timeouts.size} uncleaned timeouts detected`);
    }

    if (global.performanceMetrics.intervals.size > 0) {
      console.warn(`⚠️  ${global.performanceMetrics.intervals.size} uncleaned intervals detected`);
    }
  }
};

// Async operation tracking
global.trackAsyncOperation = async (operation, label = 'async-op') => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  try {
    const result = await operation();

    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

    console.log(`📊 ${label}: ${duration}ms, ${Math.round(memoryDelta / 1024 / 1024)}MB`);

    return result;
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.error(
      `❌ ${label} failed after ${duration}ms:`,
      error instanceof Error ? error.message : String(error)
    );
    throw error;
  }
};

// Resource cleanup helper
global.cleanupResources = () => {
  // Clear all tracked timers
  global.performanceMetrics.timeouts.forEach(clearTimeout);
  global.performanceMetrics.intervals.forEach(clearInterval);
  global.performanceMetrics.timeouts.clear();
  global.performanceMetrics.intervals.clear();

  // Force garbage collection
  if (global.gc) {
    global.gc();
  }
};

// Global cleanup on process exit
process.on('exit', () => {
  global.cleanupResources();
});

process.on('SIGINT', () => {
  global.cleanupResources();
  process.exit(0);
});

process.on('SIGTERM', () => {
  global.cleanupResources();
  process.exit(0);
});

#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('🔍 Checking for existing test user...');

    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Test user already exists:', existingUser.email);
      return existingUser;
    }

    console.log('👤 Creating test user...');

    const hashedPassword = await bcrypt.hash('TestPassword123!', 12);

    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        password: hashedPassword
      }
    });

    console.log('✅ Test user created successfully:', user.email);

    // Create some sample skill assessments
    console.log('🎯 Creating sample skill assessments...');

    const skills = [
      { name: 'JavaScript', rating: 7, confidence: 8 },
      { name: 'React', rating: 6, confidence: 7 },
      { name: 'Node.js', rating: 5, confidence: 6 },
      { name: 'TypeScript', rating: 4, confidence: 5 },
      { name: 'Python', rating: 3, confidence: 4 },
      { name: 'SQL', rating: 6, confidence: 7 }
    ];

    for (const skill of skills) {
      // First, find or create the skill
      let skillRecord = await prisma.skill.findFirst({
        where: { name: skill.name }
      });

      if (!skillRecord) {
        skillRecord = await prisma.skill.create({
          data: {
            name: skill.name,
            category: 'Technical',
            description: `${skill.name} programming skill`,
            marketDemand: 'HIGH'
          }
        });
      }

      // Create skill assessment
      await prisma.skillAssessment.upsert({
        where: {
          userId_skillId_assessmentType: {
            userId: user.id,
            skillId: skillRecord.id,
            assessmentType: 'SELF_ASSESSMENT'
          }
        },
        update: {
          selfRating: skill.rating,
          confidenceLevel: skill.confidence
        },
        create: {
          userId: user.id,
          skillId: skillRecord.id,
          selfRating: skill.rating,
          confidenceLevel: skill.confidence,
          assessmentType: 'SELF_ASSESSMENT'
        }
      });
    }

    console.log('✅ Sample skill assessments created');

    // Create a sample career assessment
    console.log('📊 Creating sample career assessment...');

    const assessment = await prisma.assessment.create({
      data: {
        userId: user.id,
        status: 'COMPLETED',
        currentStep: 10,
        completedAt: new Date(),
        responses: {
          create: [
            {
              questionKey: 'current_role',
              answerValue: 'Software Developer'
            },
            {
              questionKey: 'experience_level',
              answerValue: 'INTERMEDIATE'
            },
            {
              questionKey: 'career_goals',
              answerValue: 'Full Stack Developer'
            },
            {
              questionKey: 'skills',
              answerValue: JSON.stringify(['JavaScript', 'React', 'Node.js'])
            }
          ]
        }
      }
    });

    console.log('✅ Sample career assessment created');

    return user;
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser().catch(console.error);

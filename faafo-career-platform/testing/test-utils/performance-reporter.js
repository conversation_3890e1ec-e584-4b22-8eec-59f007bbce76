/**
 * Custom Jest Reporter for Performance Monitoring
 * Tracks test execution times, memory usage, and performance metrics
 */

class PerformanceReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options;
    this.testResults = [];
    this.suiteResults = [];
    this.startTime = Date.now();
    this.memorySnapshots = [];
  }

  onRunStart(results, options) {
    console.log('\n🚀 Performance Test Suite Started');
    console.log('='.repeat(50));
    this.startTime = Date.now();
    this.initialMemory = process.memoryUsage();

    // Take initial memory snapshot
    this.memorySnapshots.push({
      timestamp: Date.now(),
      memory: process.memoryUsage(),
      phase: 'start'
    });
  }

  onTestStart(test) {
    test.startTime = Date.now();
    test.startMemory = process.memoryUsage();
  }

  onTestResult(test, testResult, aggregatedResult) {
    const endTime = Date.now();
    const endMemory = process.memoryUsage();

    const duration = endTime - test.startTime;
    const memoryDelta = endMemory.heapUsed - test.startMemory.heapUsed;

    // Store detailed test metrics
    const testMetrics = {
      testPath: testResult.testFilePath,
      testName: test.path,
      duration,
      memoryDelta,
      memoryUsed: endMemory.heapUsed,
      status: testResult.numFailingTests > 0 ? 'failed' : 'passed',
      numTests: testResult.numPassingTests + testResult.numFailingTests,
      timestamp: endTime
    };

    this.testResults.push(testMetrics);

    // Log slow tests immediately
    if (duration > 10000) {
      // 10 seconds
      console.log(`⚠️  Slow test detected: ${test.path} (${duration}ms)`);
    }

    // Log memory-intensive tests
    if (memoryDelta > 50 * 1024 * 1024) {
      // 50MB
      console.log(
        `🧠 Memory-intensive test: ${test.path} (+${Math.round(memoryDelta / 1024 / 1024)}MB)`
      );
    }
  }

  onRunComplete(contexts, results) {
    const endTime = Date.now();
    const totalDuration = endTime - this.startTime;
    const finalMemory = process.memoryUsage();
    const totalMemoryDelta = finalMemory.heapUsed - this.initialMemory.heapUsed;

    console.log('\n📊 Performance Test Results');
    console.log('='.repeat(50));

    // Overall metrics
    console.log(`Total Duration: ${totalDuration}ms`);
    console.log(`Total Memory Delta: ${Math.round(totalMemoryDelta / 1024 / 1024)}MB`);
    console.log(`Tests Run: ${results.numTotalTests}`);
    console.log(`Tests Passed: ${results.numPassedTests}`);
    console.log(`Tests Failed: ${results.numFailedTests}`);

    // Performance analysis
    this.analyzePerformance();

    // Generate performance report
    this.generatePerformanceReport(totalDuration, totalMemoryDelta);
  }

  analyzePerformance() {
    console.log('\n🔍 Performance Analysis');
    console.log('-'.repeat(30));

    // Find slowest tests
    const slowestTests = this.testResults.sort((a, b) => b.duration - a.duration).slice(0, 5);

    console.log('Slowest Tests:');
    slowestTests.forEach((test, index) => {
      console.log(`  ${index + 1}. ${test.testName} - ${test.duration}ms`);
    });

    // Find memory-intensive tests
    const memoryIntensiveTests = this.testResults
      .sort((a, b) => b.memoryDelta - a.memoryDelta)
      .slice(0, 5);

    console.log('\nMemory-Intensive Tests:');
    memoryIntensiveTests.forEach((test, index) => {
      const memoryMB = Math.round(test.memoryDelta / 1024 / 1024);
      console.log(`  ${index + 1}. ${test.testName} - +${memoryMB}MB`);
    });

    // Performance recommendations
    this.generateRecommendations();
  }

  generateRecommendations() {
    console.log('\n💡 Performance Recommendations');
    console.log('-'.repeat(30));

    const avgDuration =
      this.testResults.reduce((sum, test) => sum + test.duration, 0) / this.testResults.length;
    const avgMemoryDelta =
      this.testResults.reduce((sum, test) => sum + test.memoryDelta, 0) / this.testResults.length;

    if (avgDuration > 5000) {
      console.log('⚠️  Average test duration is high. Consider:');
      console.log('   - Reducing test complexity');
      console.log('   - Using more mocks');
      console.log('   - Optimizing test setup/teardown');
    }

    if (avgMemoryDelta > 10 * 1024 * 1024) {
      console.log('⚠️  Average memory usage is high. Consider:');
      console.log('   - Implementing proper cleanup');
      console.log('   - Reducing test data size');
      console.log('   - Using memory-efficient patterns');
    }

    const failureRate =
      this.testResults.filter(t => t.status === 'failed').length / this.testResults.length;
    if (failureRate > 0.1) {
      console.log('⚠️  High failure rate detected. Consider:');
      console.log('   - Improving test stability');
      console.log('   - Fixing flaky tests');
      console.log('   - Better error handling');
    }
  }

  generatePerformanceReport(totalDuration, totalMemoryDelta) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalDuration,
        totalMemoryDelta: Math.round(totalMemoryDelta / 1024 / 1024),
        testsRun: this.testResults.length,
        averageDuration: Math.round(
          this.testResults.reduce((sum, test) => sum + test.duration, 0) / this.testResults.length
        ),
        averageMemoryDelta: Math.round(
          this.testResults.reduce((sum, test) => sum + test.memoryDelta, 0) /
            this.testResults.length /
            1024 /
            1024
        )
      },
      tests: this.testResults,
      memorySnapshots: this.memorySnapshots
    };

    // Save report to file
    const fs = require('fs');
    const path = require('path');

    const reportDir = path.join(process.cwd(), 'test-results', 'performance');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const reportPath = path.join(reportDir, `performance-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n📄 Performance report saved to: ${reportPath}`);
  }
}

module.exports = PerformanceReporter;

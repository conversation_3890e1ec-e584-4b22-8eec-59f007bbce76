name: Code Quality Checks

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  quality-checks:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: TypeScript type checking
        run: npm run type-check

      - name: ESLint analysis
        run: npm run lint:strict

      - name: Prettier formatting check
        run: npm run format:check

      - name: Security audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: Run unit tests
        run: npm run test:unit

      - name: Run architecture tests
        run: npm run test:architecture

      - name: Generate quality report
        run: npm run quality:check
        continue-on-error: true

      - name: Upload quality reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: quality-reports-${{ matrix.node-version }}
          path: quality-reports/
          retention-days: 30

  dependency-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Check for outdated dependencies
        run: npm outdated
        continue-on-error: true

      - name: Dependency vulnerability scan
        run: npm audit --audit-level=moderate
        continue-on-error: true

  build-test:
    runs-on: ubuntu-latest
    needs: [quality-checks]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run comprehensive tests
        run: npm run test:ci

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: .next/
          retention-days: 7

  quality-gate:
    runs-on: ubuntu-latest
    needs: [quality-checks, dependency-check, build-test]
    if: always()

    steps:
      - name: Check quality gate
        run: |
          if [[ "${{ needs.quality-checks.result }}" == "failure" ]]; then
            echo "❌ Quality checks failed"
            exit 1
          fi

          if [[ "${{ needs.build-test.result }}" == "failure" ]]; then
            echo "❌ Build or tests failed"
            exit 1
          fi

          echo "✅ All quality gates passed"

      - name: Quality gate summary
        run: |
          echo "## Quality Gate Results" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Quality Checks | ${{ needs.quality-checks.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Dependency Check | ${{ needs.dependency-check.result == 'success' && '✅ Passed' || '⚠️ Issues Found' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Build & Test | ${{ needs.build-test.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY

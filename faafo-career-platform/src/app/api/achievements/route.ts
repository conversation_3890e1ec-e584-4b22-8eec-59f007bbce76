import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { checkAndUnlockAchievements } from '@/lib/achievements';

// GET handler to retrieve user achievements
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check and unlock achievements before returning data
    await checkAndUnlockAchievements(session.user.id);

    const { searchParams } = new URL(request.url);
    const includeAll = searchParams.get('includeAll') === 'true';
    const type = searchParams.get('type');

    // Get all achievements
    const whereClause: any = { isActive: true };
    if (type) {
      whereClause.type = type;
    }

    const allAchievements = await prisma.achievement.findMany({
      where: whereClause,
      orderBy: [
        { type: 'asc' },
        { points: 'asc' }
      ]
    });

    // Get user's unlocked achievements
    const userAchievements = await prisma.userAchievement.findMany({
      where: { userId: session.user.id },
      include: { achievement: true }
    });

    const unlockedAchievementIds = new Set(userAchievements.map(ua => ua.achievementId));

    // Combine achievements with unlock status
    const achievements = allAchievements.map(achievement => ({
      id: achievement.id,
      name: achievement.title,
      title: achievement.title,
      description: achievement.description,
      type: achievement.type,
      icon: achievement.icon,
      criteria: achievement.criteria,
      points: achievement.points,
      isActive: achievement.isActive,
      isUnlocked: unlockedAchievementIds.has(achievement.id),
      unlockedAt: userAchievements.find(ua => ua.achievementId === achievement.id)?.unlockedAt?.toISOString(),
      progress: userAchievements.find(ua => ua.achievementId === achievement.id)?.progress
    }));

    const stats = {
      total: allAchievements.length,
      unlocked: userAchievements.length,
      totalPoints: userAchievements.reduce((sum, ua) => sum + ua.achievement.points, 0)
    };

    return NextResponse.json({
      success: true,
      achievements: includeAll ? achievements : achievements.filter(a => a.isUnlocked),
      total: stats.total,
      unlocked: stats.unlocked,
      totalPoints: stats.totalPoints,
      stats
    });
  } catch (error) {
    console.error('Error fetching achievements:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

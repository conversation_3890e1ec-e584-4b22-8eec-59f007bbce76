import prisma from '@/lib/prisma';

// Helper function to check and unlock achievements based on user activity
export async function checkAndUnlockAchievements(userId: string) {
  try {
    // Get user's assessments (using same data source as Progress tab)
    const assessments = await prisma.assessment.findMany({
      where: { userId },
    });

    const completedResources = assessments.filter(a => a.status === 'COMPLETED').length;
    const totalRatings = 0; // No rating system for assessments yet

    // Get user's forum activity
    const forumPosts = await prisma.forumPost.count({
      where: { authorId: userId },
    });

    const forumReplies = await prisma.forumReply.count({
      where: { authorId: userId },
    });

    // Get user's goals
    const completedGoals = await prisma.userGoal.count({
      where: { userId, status: 'COMPLETED' },
    });

    // Define achievement criteria
    const achievementChecks = [
      {
        name: 'First Steps',
        description: 'Complete your first learning resource',
        type: 'LEARNING_MILESTONE',
        criteria: { completedResources: 1 },
        check: () => completedResources >= 1,
      },
      {
        name: 'Learning Enthusiast',
        description: 'Complete 10 learning resources',
        type: 'LEARNING_MILESTONE',
        criteria: { completedResources: 10 },
        check: () => completedResources >= 10,
      },
      {
        name: 'Knowledge Seeker',
        description: 'Complete 25 learning resources',
        type: 'LEARNING_MILESTONE',
        criteria: { completedResources: 25 },
        check: () => completedResources >= 25,
      },
      {
        name: 'Community Helper',
        description: 'Make 5 forum posts or replies',
        type: 'COMMUNITY_CONTRIBUTOR',
        criteria: { forumActivity: 5 },
        check: () => (forumPosts + forumReplies) >= 5,
      },
      {
        name: 'Goal Achiever',
        description: 'Complete your first goal',
        type: 'GOAL_ACHIEVER',
        criteria: { completedGoals: 1 },
        check: () => completedGoals >= 1,
      },
      {
        name: 'Reviewer',
        description: 'Rate 5 learning resources',
        type: 'COMPLETION_BADGE',
        criteria: { ratings: 5 },
        check: () => totalRatings >= 5,
      },
    ];

    const newAchievements = [];

    for (const achievementCheck of achievementChecks) {
      if (achievementCheck.check()) {
        // Check if achievement exists
        let achievement = await prisma.achievement.findUnique({
          where: { title: achievementCheck.name },
        });

        if (!achievement) {
          // Create achievement if it doesn't exist
          achievement = await prisma.achievement.create({
            data: {
              title: achievementCheck.name,
              description: achievementCheck.description,
              type: achievementCheck.type as any,
              icon: `achievement-${achievementCheck.type.toLowerCase()}`,
              criteria: achievementCheck.criteria,
              points: 10,
            },
          });
        }

        // Check if user already has this achievement
        const existingUserAchievement = await prisma.userAchievement.findUnique({
          where: {
            userId_achievementId: {
              userId,
              achievementId: achievement.id,
            },
          },
        });

        if (!existingUserAchievement) {
          // Unlock achievement for user
          const userAchievement = await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id,
            },
            include: {
              achievement: true,
            },
          });

          newAchievements.push(userAchievement);
        }
      }
    }

    return newAchievements;
  } catch (error) {
    console.error('Error checking achievements:', error);
    return [];
  }
}

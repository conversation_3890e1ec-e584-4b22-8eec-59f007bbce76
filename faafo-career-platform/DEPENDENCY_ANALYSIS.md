# Dependency Analysis and Resolution

This document outlines the dependency conflicts found and resolved in the FAAFO
Career Platform.

## Issues Identified and Resolved

### 1. Duplicate Node Modules

**Problem**: Both root and project level had node_modules directories

- Root: `/node_modules`
- Project: `/faafo-career-platform/node_modules`

**Resolution**:

- Removed both node_modules directories
- Reinstalled dependencies using workspace configuration
- Now uses single node_modules at root level with proper workspace linking

### 2. Version Conflicts

**Problem**: Conflicting versions of the same package

- `fuse.js`: Root had `^6.6.2`, Project had `^7.1.0`

**Resolution**:

- Removed fuse.js from root dependencies
- Kept project-level version `^7.1.0` as it's more recent and actively used

### 3. Playwright Duplication

**Problem**: Playwright packages in wrong dependency sections

- `@playwright/test: ^1.53.2` in dependencies (should be devDependencies)
- `playwright: ^1.53.1` in dependencies (should be devDependencies)

**Resolution**:

- Moved both packages to devDependencies
- Playwright is only needed for testing, not production

### 4. Deprecated Dependencies

**Problem**: Using deprecated packages

- `@types/winston: ^2.4.4` - Winston provides its own types

**Resolution**:

- Removed `@types/winston` from dependencies
- Winston package already includes TypeScript definitions

### 5. Security Vulnerabilities

**Problem**: 4 moderate severity vulnerabilities in PrismJS chain

- PrismJS DOM Clobbering vulnerability
- Affects swagger-ui-react through dependency chain

**Status**: Identified but requires breaking changes to fix

- Can be resolved with `npm audit fix --force`
- Would downgrade swagger-ui-react from 5.26.2 to 3.29.0
- Recommend manual review before applying breaking changes

## Current Dependency Structure

### Production Dependencies (64 packages)

**Core Framework**:

- `next: ^14.2.5` - Next.js framework
- `react: ^18.2.0` - React library
- `react-dom: ^18.2.0` - React DOM

**Authentication & Security**:

- `next-auth: ^4.24.11` - Authentication
- `@auth/prisma-adapter: ^2.9.1` - Auth adapter
- `bcryptjs: ^3.0.2` - Password hashing

**Database & ORM**:

- `@prisma/client: ^6.8.2` - Database client
- `prisma: ^6.8.2` - Database toolkit

**UI Components**:

- `@radix-ui/*` - 15 Radix UI components
- `lucide-react: ^0.511.0` - Icons
- `tailwind-merge: ^3.3.0` - CSS utilities

**AI & External Services**:

- `@google/generative-ai: ^0.21.0` - Google AI
- `@vercel/analytics: ^1.5.0` - Analytics
- `@sentry/nextjs: ^8.47.0` - Error tracking

### Development Dependencies (35 packages)

**Testing Framework**:

- `jest: ^29.7.0` - Test runner
- `@testing-library/*` - Testing utilities
- `@playwright/test: ^1.53.2` - E2E testing
- `playwright: ^1.53.1` - Browser automation

**TypeScript & Build Tools**:

- `typescript: ^5` - TypeScript compiler
- `@types/*` - Type definitions
- `tsx: ^4.19.4` - TypeScript execution

**Code Quality**:

- `eslint: ^9` - Linting
- `eslint-config-next: 15.3.3` - Next.js ESLint config

## Monorepo Configuration

### Workspace Setup

```json
{
  "workspaces": ["faafo-career-platform"],
  "private": true
}
```

**Benefits**:

- Single node_modules at root level
- Shared dependencies across workspace packages
- Consistent dependency versions
- Reduced disk space usage

### Package Scripts

Root level scripts delegate to project:

```json
{
  "build": "cd faafo-career-platform && npm run build",
  "start": "cd faafo-career-platform && npm start",
  "dev": "cd faafo-career-platform && npm run dev"
}
```

## Dependency Management Best Practices

### 1. Regular Auditing

```bash
# Check for vulnerabilities
npm audit

# Check for outdated packages
npm outdated

# Update dependencies
npm update
```

### 2. Security Monitoring

- Monitor security advisories for critical dependencies
- Use `npm audit` in CI/CD pipeline
- Consider using tools like Snyk or Dependabot

### 3. Version Management

- Use exact versions for critical dependencies
- Use caret ranges (^) for most packages
- Pin major versions for breaking-change-prone packages

### 4. Workspace Management

- Keep shared dependencies at root level
- Project-specific dependencies in project package.json
- Use workspace protocols for internal dependencies

## Recommendations

### Immediate Actions

1. **Security Fix**: Review and apply security fixes for PrismJS vulnerabilities
2. **Dependency Cleanup**: Remove unused dependencies
3. **Version Alignment**: Ensure consistent versions across workspace

### Long-term Maintenance

1. **Automated Updates**: Set up Dependabot or Renovate
2. **Security Scanning**: Integrate security scanning in CI/CD
3. **Dependency Review**: Monthly review of dependency updates

### Performance Optimization

1. **Bundle Analysis**: Regular bundle size analysis
2. **Tree Shaking**: Ensure unused code is eliminated
3. **Dynamic Imports**: Use dynamic imports for large dependencies

## Monitoring

### Metrics to Track

- Total dependency count
- Bundle size impact
- Security vulnerability count
- Outdated dependency percentage

### Tools

- `npm ls` - Dependency tree
- `npm audit` - Security vulnerabilities
- `npm outdated` - Outdated packages
- Bundle analyzer for size impact

## Troubleshooting

### Common Issues

1. **Peer Dependency Warnings**: Usually safe to ignore if functionality works
2. **Version Conflicts**: Use `npm ls` to identify conflicting versions
3. **Missing Dependencies**: Check if dependency should be in dependencies vs
   devDependencies

### Resolution Steps

1. Clear node_modules and package-lock.json
2. Run `npm install` to get fresh dependency tree
3. Check for peer dependency warnings
4. Run tests to ensure functionality is preserved

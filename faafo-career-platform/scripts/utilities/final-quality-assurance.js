const { PrismaClient } = require('@prisma/client');
const fetch = require('node-fetch');

const prisma = new PrismaClient();

// Quality assurance checks
async function performQualityChecks() {
  console.log('🔬 COMPREHENSIVE QUALITY ASSURANCE');
  console.log('==================================\n');

  const issues = [];
  const recommendations = [];

  try {
    // Get all resources
    const resources = await prisma.learningResource.findMany({
      include: {
        careerPaths: true
      }
    });

    console.log(`📊 Analyzing ${resources.length} resources...\n`);

    // Check 1: URL Accessibility and Response Time
    console.log('🌐 Testing URL accessibility and performance...');
    for (const resource of resources) {
      try {
        const startTime = Date.now();
        const response = await fetch(resource.url, {
          method: 'HEAD',
          timeout: 15000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; QualityAssurance/1.0)'
          }
        });
        const responseTime = Date.now() - startTime;

        if (response.status !== 200) {
          issues.push({
            type: 'URL_ERROR',
            resource: resource.title,
            issue: `Status ${response.status}`,
            severity: 'HIGH'
          });
        } else if (responseTime > 5000) {
          issues.push({
            type: 'SLOW_RESPONSE',
            resource: resource.title,
            issue: `Response time: ${responseTime}ms`,
            severity: 'MEDIUM'
          });
        }
      } catch (error) {
        issues.push({
          type: 'URL_UNREACHABLE',
          resource: resource.title,
          issue: error.message,
          severity: 'HIGH'
        });
      }
    }

    // Check 2: Content Quality Indicators
    console.log('📚 Analyzing content quality indicators...');

    // Check for proper skill level distribution
    const skillLevels = {};
    resources.forEach(r => {
      skillLevels[r.skillLevel] = (skillLevels[r.skillLevel] || 0) + 1;
    });

    const beginnerPercent = ((skillLevels.BEGINNER || 0) / resources.length) * 100;
    const advancedPercent = ((skillLevels.ADVANCED || 0) / resources.length) * 100;

    if (beginnerPercent > 70) {
      recommendations.push({
        type: 'SKILL_BALANCE',
        suggestion: 'Consider adding more intermediate and advanced resources',
        impact: 'MEDIUM'
      });
    }

    if (advancedPercent < 10) {
      recommendations.push({
        type: 'ADVANCED_CONTENT',
        suggestion: 'Add more advanced-level resources for skill progression',
        impact: 'HIGH'
      });
    }

    // Check 3: Career Path Coverage
    console.log('🛤️ Analyzing career path coverage...');

    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: true
      }
    });

    careerPaths.forEach(path => {
      if (path.learningResources.length < 5) {
        issues.push({
          type: 'INSUFFICIENT_RESOURCES',
          resource: path.name,
          issue: `Only ${path.learningResources.length} resources`,
          severity: 'MEDIUM'
        });
      }

      // Check skill level distribution within career path
      const pathSkillLevels = {};
      path.learningResources.forEach(r => {
        pathSkillLevels[r.skillLevel] = (pathSkillLevels[r.skillLevel] || 0) + 1;
      });

      if (!pathSkillLevels.BEGINNER) {
        issues.push({
          type: 'MISSING_BEGINNER',
          resource: path.name,
          issue: 'No beginner-level resources',
          severity: 'HIGH'
        });
      }
    });

    // Check 4: Resource Diversity
    console.log('🎯 Analyzing resource diversity...');

    const types = {};
    const costs = {};
    const formats = {};

    resources.forEach(r => {
      types[r.type] = (types[r.type] || 0) + 1;
      costs[r.cost] = (costs[r.cost] || 0) + 1;
      formats[r.format] = (formats[r.format] || 0) + 1;
    });

    // Check if too heavily weighted toward one type
    const coursePercent = ((types.COURSE || 0) / resources.length) * 100;
    if (coursePercent > 60) {
      recommendations.push({
        type: 'TYPE_DIVERSITY',
        suggestion: 'Add more tutorials, articles, and hands-on resources',
        impact: 'MEDIUM'
      });
    }

    // Check 5: Authority and Credibility
    console.log('🏛️ Analyzing source authority...');

    const authorityDomains = [
      'google.com',
      'microsoft.com',
      'aws.amazon.com',
      'apple.com',
      'coursera.org',
      'edx.org',
      'khanacademy.org',
      'freecodecamp.org',
      'nist.gov',
      'owasp.org',
      'mozilla.org',
      'react.dev'
    ];

    let authorityCount = 0;
    resources.forEach(r => {
      const domain = new URL(r.url).hostname.replace('www.', '');
      if (authorityDomains.some(auth => domain.includes(auth))) {
        authorityCount++;
      }
    });

    const authorityPercent = (authorityCount / resources.length) * 100;
    if (authorityPercent < 60) {
      recommendations.push({
        type: 'AUTHORITY_SOURCES',
        suggestion: 'Increase proportion of resources from authoritative sources',
        impact: 'HIGH'
      });
    }

    // Generate Quality Score
    console.log('\n📊 QUALITY ASSESSMENT RESULTS');
    console.log('=============================');

    const highIssues = issues.filter(i => i.severity === 'HIGH').length;
    const mediumIssues = issues.filter(i => i.severity === 'MEDIUM').length;
    const totalIssues = issues.length;

    let qualityScore = 100;
    qualityScore -= highIssues * 10;
    qualityScore -= mediumIssues * 5;
    qualityScore = Math.max(0, qualityScore);

    console.log(`🎯 Overall Quality Score: ${qualityScore}/100`);
    console.log(`📊 Total Resources: ${resources.length}`);
    console.log(`❌ High Priority Issues: ${highIssues}`);
    console.log(`⚠️ Medium Priority Issues: ${mediumIssues}`);
    console.log(`💡 Recommendations: ${recommendations.length}`);

    // Display Issues
    if (issues.length > 0) {
      console.log('\n🚨 ISSUES FOUND:');
      console.log('================');
      issues.forEach(issue => {
        const emoji = issue.severity === 'HIGH' ? '❌' : '⚠️';
        console.log(`${emoji} ${issue.type}: ${issue.resource}`);
        console.log(`   ${issue.issue}`);
      });
    }

    // Display Recommendations
    if (recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      console.log('===================');
      recommendations.forEach(rec => {
        const emoji = rec.impact === 'HIGH' ? '🔥' : '💡';
        console.log(`${emoji} ${rec.type}: ${rec.suggestion}`);
      });
    }

    // Quality Grade
    let grade = 'F';
    if (qualityScore >= 90) grade = 'A+';
    else if (qualityScore >= 85) grade = 'A';
    else if (qualityScore >= 80) grade = 'B+';
    else if (qualityScore >= 75) grade = 'B';
    else if (qualityScore >= 70) grade = 'C+';
    else if (qualityScore >= 65) grade = 'C';
    else if (qualityScore >= 60) grade = 'D';

    console.log(`\n🏆 QUALITY GRADE: ${grade}`);

    if (qualityScore >= 85) {
      console.log('✅ Excellent! Resources meet high quality standards.');
    } else if (qualityScore >= 70) {
      console.log('👍 Good quality, but some improvements recommended.');
    } else {
      console.log('⚠️ Quality needs improvement. Address high-priority issues.');
    }

    return {
      score: qualityScore,
      grade,
      issues,
      recommendations,
      totalResources: resources.length
    };
  } catch (error) {
    console.error('❌ Quality assurance failed:', error);
    throw error;
  }
}

async function generateQualityReport() {
  try {
    const results = await performQualityChecks();

    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      qualityScore: results.score,
      grade: results.grade,
      totalResources: results.totalResources,
      issues: results.issues,
      recommendations: results.recommendations,
      summary: {
        highPriorityIssues: results.issues.filter(i => i.severity === 'HIGH').length,
        mediumPriorityIssues: results.issues.filter(i => i.severity === 'MEDIUM').length,
        totalRecommendations: results.recommendations.length
      }
    };

    const fs = require('fs');
    const reportPath = `quality-assurance-report-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n💾 Detailed report saved to: ${reportPath}`);

    return results;
  } catch (error) {
    console.error('❌ Failed to generate quality report:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

module.exports = { performQualityChecks, generateQualityReport };

if (require.main === module) {
  generateQualityReport()
    .then(results => {
      console.log('\n🎉 Quality assurance completed!');
      process.exit(results.score >= 85 ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Quality assurance failed:', error);
      process.exit(1);
    });
}

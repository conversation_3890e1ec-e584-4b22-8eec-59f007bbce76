#!/bin/bash

# FAAFO Documentation Discovery Script
# Finds ALL documentation files across the entire project

echo "🔍 FAAFO Documentation Discovery"
echo "==============================="
echo ""

# Get the project root (assuming script is run from project root)
PROJECT_ROOT="$(pwd)"
echo "📁 Searching in: $PROJECT_ROOT"
echo ""

echo "📋 ALL MARKDOWN FILES:"
echo "====================="
find . -name "*.md" -not -path "*/node_modules/*" -not -path "*/.git/*" | sort
echo ""

echo "📁 DOCUMENTATION DIRECTORIES:"
echo "============================="
find . -type d \( -name "*doc*" -o -name "*guide*" -o -name "*manual*" \) -not -path "*/node_modules/*" -not -path "*/.git/*" | sort
echo ""

echo "📄 TEST-RELATED DOCUMENTATION:"
echo "=============================="
find . -name "*test*.md" -o -name "*TEST*.md" -not -path "*/node_modules/*" -not -path "*/.git/*" | sort
echo ""

echo "📄 SPECIFIC DOCUMENTATION FILES:"
echo "================================"
echo "Looking for specific files mentioned..."

# Check for specific files mentioned by user
files_to_check=(
    "DOCUMENTATION_INDEX.md"
    "TESTING_GUIDE.md" 
    "testing-strategy.md"
    "API_DOCUMENTATION.md"
    "SETUP_GUIDE.md"
    "USER_MANUAL.md"
    "TROUBLESHOOTING.md"
    "CHANGELOG.md"
    "CONTRIBUTING.md"
)

for file in "${files_to_check[@]}"; do
    found_files=$(find . -name "$file" -not -path "*/node_modules/*" -not -path "*/.git/*")
    if [ -n "$found_files" ]; then
        echo "✅ Found: $file"
        echo "$found_files" | sed 's/^/   /'
    else
        echo "❌ Not found: $file"
    fi
done

echo ""
echo "📁 TEST DIRECTORIES:"
echo "==================="
find . -type d -name "*test*" -not -path "*/node_modules/*" -not -path "*/.git/*" | sort
echo ""

echo "📄 CONFIGURATION FILES WITH DOCS:"
echo "================================="
find . -name "*.json" -o -name "*.yaml" -o -name "*.yml" | grep -E "(doc|guide|readme|manual)" | sort
echo ""

echo "📄 TEXT FILES THAT MIGHT BE DOCS:"
echo "================================="
find . -name "*.txt" -not -path "*/node_modules/*" -not -path "*/.git/*" | sort
echo ""

echo "🔍 SUMMARY:"
echo "==========="
total_md=$(find . -name "*.md" -not -path "*/node_modules/*" -not -path "*/.git/*" | wc -l)
organized_md=$(find ./faafo-career-platform/docs -name "*.md" 2>/dev/null | wc -l)
scattered_md=$((total_md - organized_md))

echo "📊 Total markdown files: $total_md"
echo "📁 Organized in docs/: $organized_md"
echo "📄 Scattered files: $scattered_md"

if [ $scattered_md -gt 0 ]; then
    echo ""
    echo "⚠️  You have $scattered_md scattered documentation files!"
    echo "🛠️  Run the consolidation script to organize them:"
    echo "   ./faafo-career-platform/scripts/consolidate-documentation.sh"
else
    echo ""
    echo "✅ All documentation appears to be organized!"
fi

echo ""
echo "📋 SCATTERED FILES TO CONSOLIDATE:"
echo "=================================="
find . -name "*.md" -not -path "./faafo-career-platform/docs/*" -not -path "*/node_modules/*" -not -path "*/.git/*" -not -path "./faafo-career-platform/README.md" | sort

echo ""
echo "🎯 Run this to consolidate everything:"
echo "   ./faafo-career-platform/scripts/consolidate-documentation.sh"

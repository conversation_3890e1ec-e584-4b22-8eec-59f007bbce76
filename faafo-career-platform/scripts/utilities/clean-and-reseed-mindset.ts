import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanAndReseedMindset() {
  console.log('🧹 Cleaning existing mindset resources...');

  try {
    // Delete existing mindset resources (ENTREPRENEURSHIP category)
    const deletedEntrepreneurship = await prisma.learningResource.deleteMany({
      where: {
        category: 'ENTREPRENEURSHIP'
      }
    });

    console.log(`🗑️ Deleted ${deletedEntrepreneurship.count} entrepreneurship resources`);

    // Delete existing financial literacy resources that are mindset-related
    const deletedFinancial = await prisma.learningResource.deleteMany({
      where: {
        category: 'FINANCIAL_LITERACY',
        OR: [
          { title: { contains: 'Career Transition' } },
          { title: { contains: 'Emergency Fund' } },
          { title: { contains: 'Financial Planning for Career' } }
        ]
      }
    });

    console.log(`🗑️ Deleted ${deletedFinancial.count} financial planning resources`);

    console.log('✅ Cleanup completed. Now reseeding...');

    // Import and run the seeding script
    const seedStaticResources = await import('./seed-static-resources');
    await seedStaticResources.default();
  } catch (error) {
    console.error('❌ Error during cleanup and reseed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup and reseed
if (require.main === module) {
  cleanAndReseedMindset();
}

export default cleanAndReseedMindset;

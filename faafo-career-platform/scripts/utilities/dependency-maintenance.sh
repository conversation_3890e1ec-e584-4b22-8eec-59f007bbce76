#!/bin/bash

# Dependency Maintenance Script for FAAFO Career Platform
# Provides comprehensive dependency management and analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(dirname "$0")/../.."
REPORTS_DIR="$PROJECT_ROOT/dependency-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Ensure reports directory exists
mkdir -p "$REPORTS_DIR"

echo -e "${BLUE}🔧 FAAFO Career Platform - Dependency Maintenance${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${YELLOW}$1${NC}"
    echo -e "${YELLOW}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to check for security vulnerabilities
check_security() {
    print_section "Security Audit"
    
    cd "$PROJECT_ROOT"
    
    echo "🔍 Checking for security vulnerabilities..."
    if npm audit --audit-level=moderate > "$REPORTS_DIR/security-audit-$TIMESTAMP.txt" 2>&1; then
        echo -e "${GREEN}✅ No moderate or high severity vulnerabilities found${NC}"
    else
        echo -e "${YELLOW}⚠️  Security vulnerabilities detected${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/security-audit-$TIMESTAMP.txt"
        
        # Show summary
        echo ""
        echo "Summary of vulnerabilities:"
        grep -E "(moderate|high|critical)" "$REPORTS_DIR/security-audit-$TIMESTAMP.txt" | head -5
    fi
    echo ""
}

# Function to check for outdated packages
check_outdated() {
    print_section "Outdated Packages"
    
    cd "$PROJECT_ROOT"
    
    echo "📦 Checking for outdated packages..."
    if npm outdated > "$REPORTS_DIR/outdated-packages-$TIMESTAMP.txt" 2>&1; then
        echo -e "${GREEN}✅ All packages are up to date${NC}"
    else
        echo -e "${YELLOW}⚠️  Outdated packages found${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/outdated-packages-$TIMESTAMP.txt"
        
        # Show summary
        echo ""
        echo "Top 10 outdated packages:"
        head -10 "$REPORTS_DIR/outdated-packages-$TIMESTAMP.txt"
    fi
    echo ""
}

# Function to analyze dependency tree
analyze_dependencies() {
    print_section "Dependency Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "🌳 Analyzing dependency tree..."
    
    # Generate dependency tree
    npm ls --depth=0 > "$REPORTS_DIR/dependency-tree-$TIMESTAMP.txt" 2>&1
    
    # Count dependencies
    local prod_deps=$(jq '.dependencies | length' faafo-career-platform/package.json)
    local dev_deps=$(jq '.devDependencies | length' faafo-career-platform/package.json)
    local total_deps=$((prod_deps + dev_deps))
    
    echo "📊 Dependency Statistics:"
    echo "   Production dependencies: $prod_deps"
    echo "   Development dependencies: $dev_deps"
    echo "   Total dependencies: $total_deps"
    
    # Check for duplicate dependencies
    echo ""
    echo "🔍 Checking for potential duplicates..."
    npm ls --depth=1 | grep -E "UNMET|deduped" > "$REPORTS_DIR/duplicates-$TIMESTAMP.txt" 2>&1 || true
    
    if [ -s "$REPORTS_DIR/duplicates-$TIMESTAMP.txt" ]; then
        echo -e "${YELLOW}⚠️  Potential duplicates or unmet dependencies found${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/duplicates-$TIMESTAMP.txt"
    else
        echo -e "${GREEN}✅ No duplicate dependencies detected${NC}"
    fi
    echo ""
}

# Function to check bundle size impact
analyze_bundle_size() {
    print_section "Bundle Size Analysis"
    
    cd "$PROJECT_ROOT/faafo-career-platform"
    
    echo "📏 Analyzing bundle size impact..."
    
    # Check if bundle analyzer is available
    if npm list @next/bundle-analyzer > /dev/null 2>&1; then
        echo "🔍 Running bundle analysis..."
        ANALYZE=true npm run build > "$REPORTS_DIR/bundle-analysis-$TIMESTAMP.txt" 2>&1 || true
        echo "📄 Bundle analysis saved to: $REPORTS_DIR/bundle-analysis-$TIMESTAMP.txt"
    else
        echo -e "${YELLOW}⚠️  Bundle analyzer not available. Install @next/bundle-analyzer for detailed analysis.${NC}"
    fi
    echo ""
}

# Function to clean up dependencies
cleanup_dependencies() {
    print_section "Dependency Cleanup"
    
    cd "$PROJECT_ROOT"
    
    echo "🧹 Cleaning up dependencies..."
    
    # Remove node_modules and reinstall
    echo "   Removing node_modules..."
    rm -rf node_modules faafo-career-platform/node_modules
    
    echo "   Clearing npm cache..."
    npm cache clean --force
    
    echo "   Reinstalling dependencies..."
    npm install
    
    echo -e "${GREEN}✅ Dependencies cleaned and reinstalled${NC}"
    echo ""
}

# Function to update dependencies
update_dependencies() {
    print_section "Dependency Updates"
    
    cd "$PROJECT_ROOT"
    
    echo "🔄 Updating dependencies..."
    
    # Update non-breaking changes
    echo "   Updating patch and minor versions..."
    npm update > "$REPORTS_DIR/update-log-$TIMESTAMP.txt" 2>&1
    
    echo "📄 Update log saved to: $REPORTS_DIR/update-log-$TIMESTAMP.txt"
    echo -e "${GREEN}✅ Dependencies updated${NC}"
    echo ""
}

# Function to generate comprehensive report
generate_report() {
    print_section "Generating Comprehensive Report"
    
    local report_file="$REPORTS_DIR/dependency-report-$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>FAAFO Dependency Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; }
        .good { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .timestamp { color: #666; font-size: 0.9em; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 FAAFO Career Platform Dependency Report</h1>
        <p class="timestamp">Generated: $(date)</p>
    </div>
    
    <div class="section">
        <h2>📊 Summary</h2>
        <ul>
            <li><strong>Production Dependencies:</strong> $(jq '.dependencies | length' faafo-career-platform/package.json)</li>
            <li><strong>Development Dependencies:</strong> $(jq '.devDependencies | length' faafo-career-platform/package.json)</li>
            <li><strong>Security Status:</strong> $([ -f "$REPORTS_DIR/security-audit-$TIMESTAMP.txt" ] && echo "⚠️ Issues Found" || echo "✅ Clean")</li>
            <li><strong>Outdated Packages:</strong> $([ -f "$REPORTS_DIR/outdated-packages-$TIMESTAMP.txt" ] && echo "⚠️ Updates Available" || echo "✅ Up to Date")</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>📁 Generated Reports</h2>
        <ul>
            <li><a href="security-audit-$TIMESTAMP.txt">Security Audit</a></li>
            <li><a href="outdated-packages-$TIMESTAMP.txt">Outdated Packages</a></li>
            <li><a href="dependency-tree-$TIMESTAMP.txt">Dependency Tree</a></li>
            <li><a href="duplicates-$TIMESTAMP.txt">Duplicate Analysis</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>🔧 Maintenance Actions</h2>
        <p>Regular maintenance tasks to keep dependencies healthy:</p>
        <ol>
            <li>Review security vulnerabilities and apply fixes</li>
            <li>Update outdated packages (test thoroughly)</li>
            <li>Remove unused dependencies</li>
            <li>Monitor bundle size impact</li>
        </ol>
    </div>
</body>
</html>
EOF
    
    echo -e "${GREEN}📊 Comprehensive report generated: $report_file${NC}"
    echo ""
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --security       Run security audit only"
    echo "  --outdated       Check outdated packages only"
    echo "  --analyze        Analyze dependencies only"
    echo "  --cleanup        Clean and reinstall dependencies"
    echo "  --update         Update dependencies"
    echo "  --full           Run full maintenance (default)"
    echo "  --help           Show this help message"
    echo ""
}

# Parse command line arguments
SECURITY_ONLY=false
OUTDATED_ONLY=false
ANALYZE_ONLY=false
CLEANUP_ONLY=false
UPDATE_ONLY=false
FULL_MAINTENANCE=true

while [[ $# -gt 0 ]]; do
    case $1 in
        --security)
            SECURITY_ONLY=true
            FULL_MAINTENANCE=false
            shift
            ;;
        --outdated)
            OUTDATED_ONLY=true
            FULL_MAINTENANCE=false
            shift
            ;;
        --analyze)
            ANALYZE_ONLY=true
            FULL_MAINTENANCE=false
            shift
            ;;
        --cleanup)
            CLEANUP_ONLY=true
            FULL_MAINTENANCE=false
            shift
            ;;
        --update)
            UPDATE_ONLY=true
            FULL_MAINTENANCE=false
            shift
            ;;
        --full)
            FULL_MAINTENANCE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "🚀 Starting dependency maintenance at $(date)"
    echo ""
    
    # Run specific tasks based on options
    if [[ "$SECURITY_ONLY" == true ]]; then
        check_security
    elif [[ "$OUTDATED_ONLY" == true ]]; then
        check_outdated
    elif [[ "$ANALYZE_ONLY" == true ]]; then
        analyze_dependencies
    elif [[ "$CLEANUP_ONLY" == true ]]; then
        cleanup_dependencies
    elif [[ "$UPDATE_ONLY" == true ]]; then
        update_dependencies
    elif [[ "$FULL_MAINTENANCE" == true ]]; then
        # Run full maintenance
        check_security
        check_outdated
        analyze_dependencies
        analyze_bundle_size
        generate_report
    fi
    
    echo -e "${GREEN}🎉 Dependency maintenance completed!${NC}"
    echo "📁 Reports saved to: $REPORTS_DIR"
    echo "🕐 Maintenance completed at $(date)"
}

# Run main function
main "$@"

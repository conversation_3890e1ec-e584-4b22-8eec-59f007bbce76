#!/bin/bash

# Performance Optimization Script for FAAFO Career Platform
# Provides comprehensive performance analysis and optimization

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(dirname "$0")/../.."
REPORTS_DIR="$PROJECT_ROOT/performance-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Ensure reports directory exists
mkdir -p "$REPORTS_DIR"

echo -e "${BLUE}⚡ FAAFO Career Platform - Performance Optimization${NC}"
echo -e "${BLUE}==================================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${YELLOW}$1${NC}"
    echo -e "${YELLOW}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to analyze bundle size
analyze_bundle_size() {
    print_section "Bundle Size Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "📦 Analyzing bundle size..."
    
    # Build the application with bundle analyzer
    echo "🔍 Building application with bundle analyzer..."
    ANALYZE=true npm run build > "$REPORTS_DIR/bundle-build-$TIMESTAMP.log" 2>&1 || true
    
    # Generate bundle size report
    echo "📊 Generating bundle size report..."
    npx next-bundle-analyzer > "$REPORTS_DIR/bundle-analysis-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for large files
    echo "🔍 Checking for large files..."
    find .next -name "*.js" -size +100k -exec ls -lh {} \; > "$REPORTS_DIR/large-bundles-$TIMESTAMP.txt" 2>&1 || true
    
    echo "📄 Bundle analysis saved to: $REPORTS_DIR/bundle-analysis-$TIMESTAMP.txt"
    echo "📄 Large bundles report: $REPORTS_DIR/large-bundles-$TIMESTAMP.txt"
    echo ""
}

# Function to analyze image optimization
analyze_images() {
    print_section "Image Optimization Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "🖼️ Analyzing image usage..."
    
    # Find all image files
    find src public -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" -o -name "*.svg" \) > "$REPORTS_DIR/image-inventory-$TIMESTAMP.txt"
    
    # Check for large images
    find src public -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" \) -size +500k -exec ls -lh {} \; > "$REPORTS_DIR/large-images-$TIMESTAMP.txt" 2>&1 || true
    
    # Count images by type
    echo "📊 Image statistics:" > "$REPORTS_DIR/image-stats-$TIMESTAMP.txt"
    echo "Total images: $(wc -l < "$REPORTS_DIR/image-inventory-$TIMESTAMP.txt")" >> "$REPORTS_DIR/image-stats-$TIMESTAMP.txt"
    echo "Large images (>500KB): $(wc -l < "$REPORTS_DIR/large-images-$TIMESTAMP.txt")" >> "$REPORTS_DIR/image-stats-$TIMESTAMP.txt"
    
    # Check for Next.js Image component usage
    echo "🔍 Checking Next.js Image component usage..."
    grep -r "import.*Image.*from.*next/image" src/ > "$REPORTS_DIR/nextjs-image-usage-$TIMESTAMP.txt" 2>&1 || true
    grep -r "<img" src/ > "$REPORTS_DIR/html-img-usage-$TIMESTAMP.txt" 2>&1 || true
    
    echo "📄 Image inventory: $REPORTS_DIR/image-inventory-$TIMESTAMP.txt"
    echo "📄 Large images: $REPORTS_DIR/large-images-$TIMESTAMP.txt"
    echo "📄 Image statistics: $REPORTS_DIR/image-stats-$TIMESTAMP.txt"
    echo ""
}

# Function to analyze API performance
analyze_api_performance() {
    print_section "API Performance Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "🔍 Analyzing API routes..."
    
    # Find all API routes
    find src/app/api -name "route.ts" -o -name "route.js" > "$REPORTS_DIR/api-routes-$TIMESTAMP.txt"
    
    # Check for performance patterns
    echo "📊 API Performance Patterns:" > "$REPORTS_DIR/api-analysis-$TIMESTAMP.txt"
    echo "Total API routes: $(wc -l < "$REPORTS_DIR/api-routes-$TIMESTAMP.txt")" >> "$REPORTS_DIR/api-analysis-$TIMESTAMP.txt"
    
    # Check for caching implementation
    echo "🔍 Checking caching implementation..."
    grep -r "cache" src/app/api/ > "$REPORTS_DIR/api-caching-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for rate limiting
    echo "🔍 Checking rate limiting..."
    grep -r "rateLimit\|rate-limit" src/app/api/ > "$REPORTS_DIR/api-rate-limiting-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for database queries in API routes
    echo "🔍 Checking database query patterns..."
    grep -r "prisma\|findMany\|findUnique" src/app/api/ > "$REPORTS_DIR/api-db-queries-$TIMESTAMP.txt" 2>&1 || true
    
    echo "📄 API routes: $REPORTS_DIR/api-routes-$TIMESTAMP.txt"
    echo "📄 API analysis: $REPORTS_DIR/api-analysis-$TIMESTAMP.txt"
    echo ""
}

# Function to analyze database performance
analyze_database_performance() {
    print_section "Database Performance Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "🗄️ Analyzing database performance..."
    
    # Check Prisma schema for indexes
    echo "🔍 Checking database indexes..."
    grep -n "@@index\|@@unique" prisma/schema.prisma > "$REPORTS_DIR/db-indexes-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for N+1 query patterns
    echo "🔍 Checking for potential N+1 queries..."
    grep -r "findMany.*include\|findUnique.*include" src/ > "$REPORTS_DIR/db-includes-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for select optimization
    echo "🔍 Checking select optimization..."
    grep -r "select:" src/ > "$REPORTS_DIR/db-selects-$TIMESTAMP.txt" 2>&1 || true
    
    # Generate database statistics
    echo "📊 Database Schema Statistics:" > "$REPORTS_DIR/db-stats-$TIMESTAMP.txt"
    echo "Total models: $(grep -c "^model" prisma/schema.prisma)" >> "$REPORTS_DIR/db-stats-$TIMESTAMP.txt"
    echo "Total indexes: $(grep -c "@@index" prisma/schema.prisma)" >> "$REPORTS_DIR/db-stats-$TIMESTAMP.txt"
    echo "Total unique constraints: $(grep -c "@@unique" prisma/schema.prisma)" >> "$REPORTS_DIR/db-stats-$TIMESTAMP.txt"
    
    echo "📄 Database indexes: $REPORTS_DIR/db-indexes-$TIMESTAMP.txt"
    echo "📄 Database statistics: $REPORTS_DIR/db-stats-$TIMESTAMP.txt"
    echo ""
}

# Function to analyze caching strategy
analyze_caching() {
    print_section "Caching Strategy Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "🗄️ Analyzing caching implementation..."
    
    # Find caching implementations
    grep -r "cache\|Cache" src/lib/ > "$REPORTS_DIR/caching-implementation-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for Redis usage
    grep -r "redis\|Redis" src/ > "$REPORTS_DIR/redis-usage-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for memory cache usage
    grep -r "MemoryCache\|memoryCache" src/ > "$REPORTS_DIR/memory-cache-usage-$TIMESTAMP.txt" 2>&1 || true
    
    # Analyze cache configuration
    echo "📊 Caching Configuration Analysis:" > "$REPORTS_DIR/cache-analysis-$TIMESTAMP.txt"
    echo "Cache services found: $(grep -c "class.*Cache\|Cache.*Service" src/lib/ 2>/dev/null || echo "0")" >> "$REPORTS_DIR/cache-analysis-$TIMESTAMP.txt"
    echo "Redis implementations: $(grep -c "redis\|Redis" src/ 2>/dev/null || echo "0")" >> "$REPORTS_DIR/cache-analysis-$TIMESTAMP.txt"
    
    echo "📄 Caching implementation: $REPORTS_DIR/caching-implementation-$TIMESTAMP.txt"
    echo "📄 Cache analysis: $REPORTS_DIR/cache-analysis-$TIMESTAMP.txt"
    echo ""
}

# Function to check Core Web Vitals
check_core_web_vitals() {
    print_section "Core Web Vitals Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "📊 Analyzing Core Web Vitals implementation..."
    
    # Check for web-vitals implementation
    grep -r "web-vitals\|getCLS\|getFID\|getLCP" src/ > "$REPORTS_DIR/web-vitals-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for performance monitoring
    grep -r "performance\|Performance" src/ > "$REPORTS_DIR/performance-monitoring-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for Vercel Analytics
    grep -r "@vercel/analytics" src/ package.json > "$REPORTS_DIR/vercel-analytics-$TIMESTAMP.txt" 2>&1 || true
    
    echo "📄 Web Vitals implementation: $REPORTS_DIR/web-vitals-$TIMESTAMP.txt"
    echo "📄 Performance monitoring: $REPORTS_DIR/performance-monitoring-$TIMESTAMP.txt"
    echo ""
}

# Function to generate optimization recommendations
generate_recommendations() {
    print_section "Performance Optimization Recommendations"
    
    local recommendations_file="$REPORTS_DIR/optimization-recommendations-$TIMESTAMP.md"
    
    cat > "$recommendations_file" << EOF
# Performance Optimization Recommendations

Generated: $(date)

## Bundle Size Optimization
- [ ] Analyze bundle analyzer report for large dependencies
- [ ] Implement dynamic imports for heavy components
- [ ] Consider code splitting for vendor libraries
- [ ] Remove unused dependencies

## Image Optimization
- [ ] Convert large images to WebP format
- [ ] Implement responsive images with srcset
- [ ] Use Next.js Image component consistently
- [ ] Add image compression pipeline

## API Performance
- [ ] Implement response caching for static data
- [ ] Add request/response compression
- [ ] Optimize database queries with proper indexing
- [ ] Implement API rate limiting

## Database Optimization
- [ ] Add indexes for frequently queried fields
- [ ] Optimize N+1 queries with proper includes
- [ ] Implement query result caching
- [ ] Use select optimization for large datasets

## Caching Strategy
- [ ] Implement Redis for production caching
- [ ] Add cache invalidation strategies
- [ ] Optimize cache TTL values
- [ ] Monitor cache hit rates

## Core Web Vitals
- [ ] Implement web-vitals monitoring
- [ ] Optimize Largest Contentful Paint (LCP)
- [ ] Reduce First Input Delay (FID)
- [ ] Minimize Cumulative Layout Shift (CLS)

## Infrastructure
- [ ] Enable CDN for static assets
- [ ] Implement service worker for offline functionality
- [ ] Use edge functions for global performance
- [ ] Set up performance monitoring alerts
EOF
    
    echo -e "${GREEN}📋 Optimization recommendations generated: $recommendations_file${NC}"
    echo ""
}

# Function to generate comprehensive performance report
generate_performance_report() {
    print_section "Generating Performance Report"
    
    local report_file="$REPORTS_DIR/performance-report-$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>FAAFO Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; }
        .metric { background: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .good { color: green; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .critical { color: red; font-weight: bold; }
        .timestamp { color: #666; font-size: 0.9em; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ FAAFO Career Platform Performance Report</h1>
        <p class="timestamp">Generated: $(date)</p>
    </div>
    
    <div class="section">
        <h2>📊 Performance Summary</h2>
        <div class="metric">
            <strong>Bundle Analysis:</strong> <span class="good">✅ Completed</span>
        </div>
        <div class="metric">
            <strong>Image Optimization:</strong> <span class="warning">⚠️ Review Required</span>
        </div>
        <div class="metric">
            <strong>API Performance:</strong> <span class="good">✅ Analyzed</span>
        </div>
        <div class="metric">
            <strong>Database Performance:</strong> <span class="good">✅ Analyzed</span>
        </div>
        <div class="metric">
            <strong>Caching Strategy:</strong> <span class="good">✅ Implemented</span>
        </div>
    </div>
    
    <div class="section">
        <h2>📁 Generated Reports</h2>
        <ul>
            <li><a href="bundle-analysis-$TIMESTAMP.txt">Bundle Size Analysis</a></li>
            <li><a href="image-stats-$TIMESTAMP.txt">Image Optimization Report</a></li>
            <li><a href="api-analysis-$TIMESTAMP.txt">API Performance Analysis</a></li>
            <li><a href="db-stats-$TIMESTAMP.txt">Database Performance Report</a></li>
            <li><a href="cache-analysis-$TIMESTAMP.txt">Caching Strategy Analysis</a></li>
            <li><a href="optimization-recommendations-$TIMESTAMP.md">Optimization Recommendations</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>🎯 Next Steps</h2>
        <ol>
            <li>Review bundle analysis for optimization opportunities</li>
            <li>Implement image optimization recommendations</li>
            <li>Optimize database queries based on analysis</li>
            <li>Enhance caching strategy for better performance</li>
            <li>Monitor Core Web Vitals and set up alerts</li>
        </ol>
    </div>
</body>
</html>
EOF
    
    echo -e "${GREEN}📊 Performance report generated: $report_file${NC}"
    echo ""
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --bundle         Analyze bundle size only"
    echo "  --images         Analyze image optimization only"
    echo "  --api            Analyze API performance only"
    echo "  --database       Analyze database performance only"
    echo "  --caching        Analyze caching strategy only"
    echo "  --web-vitals     Check Core Web Vitals only"
    echo "  --full           Run full performance analysis (default)"
    echo "  --help           Show this help message"
    echo ""
}

# Parse command line arguments
BUNDLE_ONLY=false
IMAGES_ONLY=false
API_ONLY=false
DATABASE_ONLY=false
CACHING_ONLY=false
WEB_VITALS_ONLY=false
FULL_ANALYSIS=true

while [[ $# -gt 0 ]]; do
    case $1 in
        --bundle)
            BUNDLE_ONLY=true
            FULL_ANALYSIS=false
            shift
            ;;
        --images)
            IMAGES_ONLY=true
            FULL_ANALYSIS=false
            shift
            ;;
        --api)
            API_ONLY=true
            FULL_ANALYSIS=false
            shift
            ;;
        --database)
            DATABASE_ONLY=true
            FULL_ANALYSIS=false
            shift
            ;;
        --caching)
            CACHING_ONLY=true
            FULL_ANALYSIS=false
            shift
            ;;
        --web-vitals)
            WEB_VITALS_ONLY=true
            FULL_ANALYSIS=false
            shift
            ;;
        --full)
            FULL_ANALYSIS=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "🚀 Starting performance optimization analysis at $(date)"
    echo ""
    
    # Run specific analysis based on options
    if [[ "$BUNDLE_ONLY" == true ]]; then
        analyze_bundle_size
    elif [[ "$IMAGES_ONLY" == true ]]; then
        analyze_images
    elif [[ "$API_ONLY" == true ]]; then
        analyze_api_performance
    elif [[ "$DATABASE_ONLY" == true ]]; then
        analyze_database_performance
    elif [[ "$CACHING_ONLY" == true ]]; then
        analyze_caching
    elif [[ "$WEB_VITALS_ONLY" == true ]]; then
        check_core_web_vitals
    elif [[ "$FULL_ANALYSIS" == true ]]; then
        # Run full performance analysis
        analyze_bundle_size
        analyze_images
        analyze_api_performance
        analyze_database_performance
        analyze_caching
        check_core_web_vitals
        generate_recommendations
        generate_performance_report
    fi
    
    echo -e "${GREEN}🎉 Performance optimization analysis completed!${NC}"
    echo "📁 Reports saved to: $REPORTS_DIR"
    echo "🕐 Analysis completed at $(date)"
}

# Run main function
main "$@"

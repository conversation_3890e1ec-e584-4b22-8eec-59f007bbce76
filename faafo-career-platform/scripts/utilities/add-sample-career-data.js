const { PrismaClient } = require('@prisma/client');

async function addSampleCareerData() {
  const prisma = new PrismaClient();

  // Test the connection first
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return;
  }

  try {
    console.log('🔧 Adding sample career data for testing...');

    // Add a rule that matches work-life balance trigger
    const existingRule = await prisma.suggestionRule.findFirst({
      where: {
        questionKey: 'dissatisfaction_triggers',
        answerValue: {
          equals: 'work_life_balance'
        }
      }
    });

    if (!existingRule) {
      // Find the freelance web developer career path
      const freelanceCareer = await prisma.careerPath.findFirst({
        where: { slug: 'freelance-web-developer' }
      });

      if (freelanceCareer) {
        await prisma.suggestionRule.create({
          data: {
            careerPathId: freelanceCareer.id,
            questionKey: 'dissatisfaction_triggers',
            answerValue: 'work_life_balance',
            weight: 3,
            notes: 'Work-life balance issues strongly suggest freelancing for flexibility.'
          }
        });

        console.log('✅ Added career path rule for work-life balance');
      }
    } else {
      console.log('✅ Career path rule already exists');
    }

    // Add a rule for unemployed seeking status
    const unemployedRule = await prisma.suggestionRule.findFirst({
      where: {
        questionKey: 'current_employment_status',
        answerValue: {
          equals: 'unemployed_seeking'
        }
      }
    });

    if (!unemployedRule) {
      const freelanceCareer = await prisma.careerPath.findFirst({
        where: { slug: 'freelance-web-developer' }
      });

      if (freelanceCareer) {
        await prisma.suggestionRule.create({
          data: {
            careerPathId: freelanceCareer.id,
            questionKey: 'current_employment_status',
            answerValue: 'unemployed_seeking',
            weight: 2,
            notes: 'Unemployed status makes freelancing a viable quick-start option.'
          }
        });

        console.log('✅ Added career path rule for unemployed seeking');
      }
    } else {
      console.log('✅ Unemployed seeking rule already exists');
    }

    // Add a rule for experience level
    const experienceRule = await prisma.suggestionRule.findFirst({
      where: {
        questionKey: 'years_experience',
        answerValue: {
          equals: '6-10'
        }
      }
    });

    if (!experienceRule) {
      const freelanceCareer = await prisma.careerPath.findFirst({
        where: { slug: 'freelance-web-developer' }
      });

      if (freelanceCareer) {
        await prisma.suggestionRule.create({
          data: {
            careerPathId: freelanceCareer.id,
            questionKey: 'years_experience',
            answerValue: '6-10',
            weight: 2.5,
            notes: 'Mid-level experience is perfect for freelancing transition.'
          }
        });

        console.log('✅ Added career path rule for 6-10 years experience');
      }
    } else {
      console.log('✅ Experience rule already exists');
    }

    console.log('\n🎯 Sample career data setup complete!');
    console.log('Now the assessment should match career paths and generate AI insights.');
  } catch (error) {
    console.error('❌ Error adding sample career data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addSampleCareerData();

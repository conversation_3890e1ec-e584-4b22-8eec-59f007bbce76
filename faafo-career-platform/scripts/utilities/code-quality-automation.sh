#!/bin/bash

# Code Quality Automation Script for FAAFO Career Platform
# Provides comprehensive code quality checks and automated fixes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(dirname "$0")/../.."
REPORTS_DIR="$PROJECT_ROOT/quality-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Ensure reports directory exists
mkdir -p "$REPORTS_DIR"

echo -e "${BLUE}🔧 FAAFO Career Platform - Code Quality Automation${NC}"
echo -e "${BLUE}==================================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${YELLOW}$1${NC}"
    echo -e "${YELLOW}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to run TypeScript type checking
run_typescript_check() {
    print_section "TypeScript Type Checking"
    
    cd "$PROJECT_ROOT"
    
    echo "🔍 Running TypeScript compiler..."
    if npx tsc --noEmit > "$REPORTS_DIR/typescript-check-$TIMESTAMP.log" 2>&1; then
        echo -e "${GREEN}✅ TypeScript type checking passed${NC}"
        return 0
    else
        echo -e "${RED}❌ TypeScript type checking failed${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/typescript-check-$TIMESTAMP.log"
        echo ""
        echo "Top 10 TypeScript errors:"
        head -10 "$REPORTS_DIR/typescript-check-$TIMESTAMP.log"
        return 1
    fi
}

# Function to run ESLint
run_eslint() {
    print_section "ESLint Code Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "🔍 Running ESLint..."
    if npx eslint . --ext .ts,.tsx,.js,.jsx --format json > "$REPORTS_DIR/eslint-results-$TIMESTAMP.json" 2>&1; then
        echo -e "${GREEN}✅ ESLint analysis passed${NC}"
        return 0
    else
        echo -e "${RED}❌ ESLint found issues${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/eslint-results-$TIMESTAMP.json"
        
        # Generate human-readable report
        npx eslint . --ext .ts,.tsx,.js,.jsx --format table > "$REPORTS_DIR/eslint-table-$TIMESTAMP.txt" 2>&1 || true
        echo "📄 Human-readable report: $REPORTS_DIR/eslint-table-$TIMESTAMP.txt"
        return 1
    fi
}

# Function to run ESLint with auto-fix
run_eslint_fix() {
    print_section "ESLint Auto-Fix"
    
    cd "$PROJECT_ROOT"
    
    echo "🔧 Running ESLint with auto-fix..."
    npx eslint . --ext .ts,.tsx,.js,.jsx --fix > "$REPORTS_DIR/eslint-fix-$TIMESTAMP.log" 2>&1 || true
    echo -e "${GREEN}✅ ESLint auto-fix completed${NC}"
    echo "📄 Fix log saved to: $REPORTS_DIR/eslint-fix-$TIMESTAMP.log"
}

# Function to run Prettier
run_prettier_check() {
    print_section "Prettier Code Formatting"
    
    cd "$PROJECT_ROOT"
    
    echo "🎨 Checking code formatting with Prettier..."
    if npx prettier --check . > "$REPORTS_DIR/prettier-check-$TIMESTAMP.log" 2>&1; then
        echo -e "${GREEN}✅ Code formatting is consistent${NC}"
        return 0
    else
        echo -e "${RED}❌ Code formatting issues found${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/prettier-check-$TIMESTAMP.log"
        return 1
    fi
}

# Function to run Prettier with auto-fix
run_prettier_fix() {
    print_section "Prettier Auto-Format"
    
    cd "$PROJECT_ROOT"
    
    echo "🎨 Running Prettier auto-format..."
    npx prettier --write . > "$REPORTS_DIR/prettier-fix-$TIMESTAMP.log" 2>&1
    echo -e "${GREEN}✅ Code formatting completed${NC}"
    echo "📄 Format log saved to: $REPORTS_DIR/prettier-fix-$TIMESTAMP.log"
}

# Function to analyze code complexity
analyze_complexity() {
    print_section "Code Complexity Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "📊 Analyzing code complexity..."
    
    # Count lines of code
    local total_lines=$(find src -name "*.ts" -o -name "*.tsx" | xargs wc -l | tail -1 | awk '{print $1}')
    local total_files=$(find src -name "*.ts" -o -name "*.tsx" | wc -l)
    local avg_lines_per_file=$((total_lines / total_files))
    
    # Find large files (>500 lines)
    echo "🔍 Finding large files (>500 lines)..."
    find src -name "*.ts" -o -name "*.tsx" | xargs wc -l | awk '$1 > 500 {print $2 ": " $1 " lines"}' > "$REPORTS_DIR/large-files-$TIMESTAMP.txt"
    
    # Find files with many imports (potential coupling issues)
    echo "🔍 Finding files with many imports (>20)..."
    find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "^import" | while read file; do
        import_count=$(grep "^import" "$file" | wc -l)
        if [ "$import_count" -gt 20 ]; then
            echo "$file: $import_count imports"
        fi
    done > "$REPORTS_DIR/high-coupling-$TIMESTAMP.txt"
    
    echo "📊 Code Complexity Metrics:"
    echo "   Total lines of code: $total_lines"
    echo "   Total files: $total_files"
    echo "   Average lines per file: $avg_lines_per_file"
    echo "📄 Large files report: $REPORTS_DIR/large-files-$TIMESTAMP.txt"
    echo "📄 High coupling report: $REPORTS_DIR/high-coupling-$TIMESTAMP.txt"
    echo ""
}

# Function to check for security issues
run_security_audit() {
    print_section "Security Audit"
    
    cd "$PROJECT_ROOT"
    
    echo "🔒 Running npm security audit..."
    if npm audit --audit-level=moderate > "$REPORTS_DIR/security-audit-$TIMESTAMP.txt" 2>&1; then
        echo -e "${GREEN}✅ No moderate or high severity vulnerabilities found${NC}"
    else
        echo -e "${YELLOW}⚠️  Security vulnerabilities detected${NC}"
        echo "📄 Report saved to: $REPORTS_DIR/security-audit-$TIMESTAMP.txt"
        
        # Show summary
        echo ""
        echo "Security vulnerability summary:"
        grep -E "(moderate|high|critical)" "$REPORTS_DIR/security-audit-$TIMESTAMP.txt" | head -5 || echo "No specific vulnerabilities listed"
    fi
    echo ""
}

# Function to check dependencies
check_dependencies() {
    print_section "Dependency Analysis"
    
    cd "$PROJECT_ROOT"
    
    echo "📦 Checking for outdated dependencies..."
    npm outdated > "$REPORTS_DIR/outdated-deps-$TIMESTAMP.txt" 2>&1 || true
    
    echo "📦 Analyzing dependency tree..."
    npm ls --depth=0 > "$REPORTS_DIR/dependency-tree-$TIMESTAMP.txt" 2>&1 || true
    
    # Check for unused dependencies
    echo "📦 Checking for unused dependencies..."
    if command -v depcheck >/dev/null 2>&1; then
        npx depcheck > "$REPORTS_DIR/unused-deps-$TIMESTAMP.txt" 2>&1 || true
        echo "📄 Unused dependencies report: $REPORTS_DIR/unused-deps-$TIMESTAMP.txt"
    else
        echo "⚠️  depcheck not available. Install with: npm install -g depcheck"
    fi
    
    echo "📄 Outdated dependencies: $REPORTS_DIR/outdated-deps-$TIMESTAMP.txt"
    echo "📄 Dependency tree: $REPORTS_DIR/dependency-tree-$TIMESTAMP.txt"
    echo ""
}

# Function to generate comprehensive quality report
generate_quality_report() {
    print_section "Generating Quality Report"
    
    local report_file="$REPORTS_DIR/quality-report-$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>FAAFO Code Quality Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; }
        .pass { color: green; font-weight: bold; }
        .fail { color: red; font-weight: bold; }
        .warn { color: orange; font-weight: bold; }
        .timestamp { color: #666; font-size: 0.9em; }
        .metric { background: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 FAAFO Career Platform Code Quality Report</h1>
        <p class="timestamp">Generated: $(date)</p>
    </div>
    
    <div class="section">
        <h2>📊 Quality Summary</h2>
        <div class="metric">
            <strong>TypeScript:</strong> $([ -f "$REPORTS_DIR/typescript-check-$TIMESTAMP.log" ] && echo '<span class="fail">❌ Issues Found</span>' || echo '<span class="pass">✅ Passed</span>')
        </div>
        <div class="metric">
            <strong>ESLint:</strong> $([ -f "$REPORTS_DIR/eslint-results-$TIMESTAMP.json" ] && echo '<span class="fail">❌ Issues Found</span>' || echo '<span class="pass">✅ Passed</span>')
        </div>
        <div class="metric">
            <strong>Prettier:</strong> $([ -f "$REPORTS_DIR/prettier-check-$TIMESTAMP.log" ] && echo '<span class="fail">❌ Formatting Issues</span>' || echo '<span class="pass">✅ Formatted</span>')
        </div>
        <div class="metric">
            <strong>Security:</strong> $([ -f "$REPORTS_DIR/security-audit-$TIMESTAMP.txt" ] && echo '<span class="warn">⚠️ Check Required</span>' || echo '<span class="pass">✅ Clean</span>')
        </div>
    </div>
    
    <div class="section">
        <h2>📁 Generated Reports</h2>
        <ul>
            <li><a href="typescript-check-$TIMESTAMP.log">TypeScript Check Results</a></li>
            <li><a href="eslint-results-$TIMESTAMP.json">ESLint Results (JSON)</a></li>
            <li><a href="eslint-table-$TIMESTAMP.txt">ESLint Results (Table)</a></li>
            <li><a href="prettier-check-$TIMESTAMP.log">Prettier Check Results</a></li>
            <li><a href="security-audit-$TIMESTAMP.txt">Security Audit</a></li>
            <li><a href="large-files-$TIMESTAMP.txt">Large Files Analysis</a></li>
            <li><a href="high-coupling-$TIMESTAMP.txt">High Coupling Analysis</a></li>
            <li><a href="outdated-deps-$TIMESTAMP.txt">Outdated Dependencies</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>🔧 Recommended Actions</h2>
        <ol>
            <li>Review and fix TypeScript errors</li>
            <li>Address ESLint warnings and errors</li>
            <li>Format code with Prettier</li>
            <li>Update outdated dependencies</li>
            <li>Review security vulnerabilities</li>
            <li>Refactor large files (>500 lines)</li>
            <li>Reduce coupling in high-import files</li>
        </ol>
    </div>
</body>
</html>
EOF
    
    echo -e "${GREEN}📊 Quality report generated: $report_file${NC}"
    echo ""
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --check          Run all quality checks (default)"
    echo "  --fix            Run auto-fixes (ESLint + Prettier)"
    echo "  --typescript     Run TypeScript check only"
    echo "  --eslint         Run ESLint only"
    echo "  --prettier       Run Prettier check only"
    echo "  --security       Run security audit only"
    echo "  --complexity     Run complexity analysis only"
    echo "  --dependencies   Check dependencies only"
    echo "  --help           Show this help message"
    echo ""
}

# Parse command line arguments
CHECK_MODE=true
FIX_MODE=false
TYPESCRIPT_ONLY=false
ESLINT_ONLY=false
PRETTIER_ONLY=false
SECURITY_ONLY=false
COMPLEXITY_ONLY=false
DEPENDENCIES_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --check)
            CHECK_MODE=true
            shift
            ;;
        --fix)
            FIX_MODE=true
            CHECK_MODE=false
            shift
            ;;
        --typescript)
            TYPESCRIPT_ONLY=true
            CHECK_MODE=false
            shift
            ;;
        --eslint)
            ESLINT_ONLY=true
            CHECK_MODE=false
            shift
            ;;
        --prettier)
            PRETTIER_ONLY=true
            CHECK_MODE=false
            shift
            ;;
        --security)
            SECURITY_ONLY=true
            CHECK_MODE=false
            shift
            ;;
        --complexity)
            COMPLEXITY_ONLY=true
            CHECK_MODE=false
            shift
            ;;
        --dependencies)
            DEPENDENCIES_ONLY=true
            CHECK_MODE=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    local exit_code=0
    
    echo "🚀 Starting code quality automation at $(date)"
    echo ""
    
    # Run specific tasks based on options
    if [[ "$FIX_MODE" == true ]]; then
        run_eslint_fix
        run_prettier_fix
    elif [[ "$TYPESCRIPT_ONLY" == true ]]; then
        run_typescript_check || exit_code=1
    elif [[ "$ESLINT_ONLY" == true ]]; then
        run_eslint || exit_code=1
    elif [[ "$PRETTIER_ONLY" == true ]]; then
        run_prettier_check || exit_code=1
    elif [[ "$SECURITY_ONLY" == true ]]; then
        run_security_audit
    elif [[ "$COMPLEXITY_ONLY" == true ]]; then
        analyze_complexity
    elif [[ "$DEPENDENCIES_ONLY" == true ]]; then
        check_dependencies
    elif [[ "$CHECK_MODE" == true ]]; then
        # Run all checks
        run_typescript_check || exit_code=1
        run_eslint || exit_code=1
        run_prettier_check || exit_code=1
        run_security_audit
        analyze_complexity
        check_dependencies
        generate_quality_report
    fi
    
    # Final summary
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}🎉 Code quality automation completed successfully!${NC}"
    else
        echo -e "${RED}💥 Some quality checks failed. Review the reports for details.${NC}"
    fi
    
    echo "📁 Reports saved to: $REPORTS_DIR"
    echo "🕐 Quality automation completed at $(date)"
    
    exit $exit_code
}

# Run main function
main "$@"

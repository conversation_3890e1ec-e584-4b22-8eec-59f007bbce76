import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// URLs to verify (updated with new working URLs)
const mindsetResourceUrls = [
  'https://www.linkedin.com/pulse/overcoming-six-fears-midlife-career-change-guide-joanne-savoie-malone-xwpme',
  'https://hbr.org/2023/11/why-career-transition-is-so-hard',
  'https://www.mayoclinic.org/healthy-lifestyle/consumer-health/in-depth/mindfulness-exercises/art-20046356',
  'https://hbr.org/2023/09/how-to-think-strategically-about-a-career-transition',
  'https://www.tapevents.mil/resources',
  'https://www.entrepreneur.com/growing-a-business/how-to-turn-your-side-hustle-into-a-full-time-business/325890',
  'https://www.linkedin.com/pulse/overcoming-career-fears-guide-building-confidence-moving-sulista-1amwe',
  'https://hbr.org/2021/02/stop-telling-women-they-have-imposter-syndrome',
  'https://self-compassion.org/the-three-elements-of-self-compassion-2/',
  'https://www.linkedin.com/pulse/complete-guide-career-pivoting-jenny-foss/',
  'https://designingyour.life/',
  'https://www.ted.com/talks/scott_dinsmore_how_to_find_work_you_love',
  'https://thecareerchangepodcast.com/',
  'https://www.nocodeinstitute.io/post/overcome-fear-of-job-search-in-midlife-career-change',
  'https://www.linkedin.com/pulse/overcoming-fear-career-change-balancing-risk-reward-theresa-white-ghpgf'
];

async function verifyUrls() {
  console.log('🔍 Verifying mindset resource URLs...\n');

  let workingUrls = 0;
  let brokenUrls = 0;

  for (const url of mindsetResourceUrls) {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ResourceChecker/1.0)'
        }
      });

      if (response.ok) {
        console.log(`✅ ${url} - Status: ${response.status}`);
        workingUrls++;
      } else {
        console.log(`❌ ${url} - Status: ${response.status}`);
        brokenUrls++;
      }
    } catch (error) {
      console.log(`❌ ${url} - Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      brokenUrls++;
    }

    // Add a small delay to be respectful to servers
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log(`\n📊 Summary:`);
  console.log(`✅ Working URLs: ${workingUrls}`);
  console.log(`❌ Broken URLs: ${brokenUrls}`);
  console.log(`📈 Success Rate: ${((workingUrls / mindsetResourceUrls.length) * 100).toFixed(1)}%`);
}

async function verifyDatabaseResources() {
  console.log('\n🗄️ Verifying mindset resources in database...\n');

  try {
    const mindsetResources = await prisma.learningResource.findMany({
      where: {
        category: 'ENTREPRENEURSHIP' // Most mindset resources are categorized as ENTREPRENEURSHIP
      },
      select: {
        title: true,
        url: true,
        author: true,
        type: true
      }
    });

    console.log(`Found ${mindsetResources.length} mindset resources in database:`);
    mindsetResources.forEach((resource, index) => {
      console.log(`${index + 1}. ${resource.title} (${resource.type}) - ${resource.author}`);
    });

    // Also check financial literacy resources
    const financialResources = await prisma.learningResource.findMany({
      where: {
        category: 'FINANCIAL_LITERACY'
      },
      select: {
        title: true,
        url: true,
        author: true,
        type: true
      }
    });

    console.log(`\nFound ${financialResources.length} financial planning resources:`);
    financialResources.forEach((resource, index) => {
      console.log(`${index + 1}. ${resource.title} (${resource.type}) - ${resource.author}`);
    });
  } catch (error) {
    console.error('❌ Error querying database:', error);
  }
}

async function main() {
  try {
    await verifyUrls();
    await verifyDatabaseResources();
  } catch (error) {
    console.error('❌ Verification failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification
if (require.main === module) {
  main();
}

export default main;

{"semi": true, "trailingComma": "none", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": "*.json", "options": {"singleQuote": false}}, {"files": "*.yml", "options": {"singleQuote": false, "tabWidth": 2}}, {"files": "*.yaml", "options": {"singleQuote": false, "tabWidth": 2}}]}